package tools

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/arien-ai/cli/internal/ai/providers"
)

// GlobTool implements file pattern matching functionality
type GlobTool struct {
	*BaseTool
}

// NewGlobTool creates a new glob tool
func NewGlobTool() *GlobTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"pattern": map[string]interface{}{
				"type":        "string",
				"description": "File pattern to match (supports wildcards: *, ?, [abc], **). Use ** for recursive matching.",
			},
			"path": map[string]interface{}{
				"type":        "string",
				"description": "Base directory to search in. Defaults to current directory.",
			},
			"include_dirs": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether to include directories in results. Defaults to false (files only).",
			},
			"include_hidden": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether to include hidden files/directories (starting with .). Defaults to false.",
			},
			"max_results": map[string]interface{}{
				"type":        "number",
				"description": "Maximum number of results to return. Defaults to 100.",
			},
			"sort_by": map[string]interface{}{
				"type":        "string",
				"description": "Sort results by: 'name', 'size', 'modified', 'created'. Defaults to 'modified'.",
				"enum":        []string{"name", "size", "modified", "created"},
			},
			"sort_order": map[string]interface{}{
				"type":        "string",
				"description": "Sort order: 'asc' or 'desc'. Defaults to 'desc' (newest first).",
				"enum":        []string{"asc", "desc"},
			},
			"exclude_patterns": map[string]interface{}{
				"type":        "array",
				"description": "Patterns to exclude from results (e.g., ['*.tmp', '*.log']).",
				"items": map[string]interface{}{
					"type": "string",
				},
			},
		},
		"required": []string{"pattern"},
	}

	description := `Fast file pattern matching tool that finds files by name and pattern.

USAGE GUIDELINES:
- Uses glob patterns to find files and directories by name
- Returns matching paths sorted by modification time (newest first)
- Supports advanced wildcards and recursive directory traversal
- Provides detailed file information including size, permissions, and timestamps
- Excludes hidden files and common temporary files by default

WHEN TO USE:
- Finding files by name patterns or extensions
- Locating specific file types across directory trees
- Building file lists for processing or analysis
- Discovering files with specific naming conventions
- File system exploration and organization tasks

WHEN NOT TO USE:
- For searching file contents (use grep tool instead)
- For executing commands on found files (use bash tool after finding)
- For simple directory listing (use bash tool with ls)
- For very specific content-based searches

GLOB PATTERNS:
- * : Matches any characters except path separator
- ? : Matches any single character
- [abc] : Matches any character in brackets
- [a-z] : Matches any character in range
- ** : Matches directories recursively
- {a,b} : Matches either a or b (brace expansion)

EXAMPLES:
- Find Go files: {"pattern": "*.go"}
- Find test files: {"pattern": "*_test.go", "path": "./src"}
- Recursive search: {"pattern": "**/*.json"}
- Multiple extensions: {"pattern": "*.{js,ts,jsx,tsx}"}
- Find config files: {"pattern": "*config*", "include_dirs": true}
- Exclude patterns: {"pattern": "*.log", "exclude_patterns": ["*.tmp"]}

PERFORMANCE TIPS:
- Use specific patterns to limit search scope
- Set reasonable max_results for large directories
- Use exclude_patterns to filter unwanted files
- Consider include_dirs setting based on your needs`

	return &GlobTool{
		BaseTool: NewBaseTool("glob", description, parameters),
	}
}

// Execute executes the glob pattern matching
func (t *GlobTool) Execute(ctx context.Context, args map[string]interface{}) (*providers.ToolResult, error) {
	// Validate arguments
	if err := t.ValidateArgs(args); err != nil {
		return providers.NewErrorToolResult("", err), nil
	}

	pattern, ok := args["pattern"].(string)
	if !ok {
		return providers.NewErrorToolResult("", fmt.Errorf("pattern must be a string")), nil
	}

	// Get optional parameters
	searchPath := "."
	if p, ok := args["path"].(string); ok {
		searchPath = p
	}

	includeDirs := false
	if id, ok := args["include_dirs"].(bool); ok {
		includeDirs = id
	}

	includeHidden := false
	if ih, ok := args["include_hidden"].(bool); ok {
		includeHidden = ih
	}

	maxResults := 100
	if mr, ok := args["max_results"].(float64); ok {
		maxResults = int(mr)
	}

	sortBy := "modified"
	if sb, ok := args["sort_by"].(string); ok {
		sortBy = sb
	}

	sortOrder := "desc"
	if so, ok := args["sort_order"].(string); ok {
		sortOrder = so
	}

	var excludePatterns []string
	if ep, ok := args["exclude_patterns"].([]interface{}); ok {
		excludePatterns = make([]string, len(ep))
		for i, pattern := range ep {
			if patternStr, ok := pattern.(string); ok {
				excludePatterns[i] = patternStr
			}
		}
	}

	// Perform glob search
	matches, err := t.globSearch(ctx, searchPath, pattern, includeDirs, includeHidden, excludePatterns, maxResults)
	if err != nil {
		return providers.NewErrorToolResult("", err), nil
	}

	// Sort results
	t.sortMatches(matches, sortBy, sortOrder)

	// Prepare result
	result := &providers.ToolResult{
		Success: true,
		Data: map[string]interface{}{
			"pattern":       pattern,
			"path":          searchPath,
			"matches_found": len(matches),
			"matches":       matches,
			"search_params": map[string]interface{}{
				"include_dirs":      includeDirs,
				"include_hidden":    includeHidden,
				"max_results":       maxResults,
				"sort_by":           sortBy,
				"sort_order":        sortOrder,
				"exclude_patterns":  excludePatterns,
			},
		},
	}

	if len(matches) == 0 {
		result.Content = fmt.Sprintf("No files found matching pattern '%s' in path '%s'", pattern, searchPath)
	} else {
		result.Content = fmt.Sprintf("Found %d files matching pattern '%s'", len(matches), pattern)
	}

	return result, nil
}

// FileInfo represents information about a matched file
type FileInfo struct {
	Path        string    `json:"path"`
	Name        string    `json:"name"`
	Size        int64     `json:"size"`
	Mode        string    `json:"mode"`
	ModTime     time.Time `json:"mod_time"`
	IsDir       bool      `json:"is_dir"`
	Extension   string    `json:"extension"`
	RelativePath string   `json:"relative_path"`
}

// globSearch performs the actual glob search
func (t *GlobTool) globSearch(ctx context.Context, searchPath, pattern string, includeDirs, includeHidden bool, excludePatterns []string, maxResults int) ([]FileInfo, error) {
	var matches []FileInfo
	
	// Convert to absolute path
	absSearchPath, err := filepath.Abs(searchPath)
	if err != nil {
		return nil, fmt.Errorf("invalid search path: %w", err)
	}

	// Build full pattern
	fullPattern := filepath.Join(absSearchPath, pattern)
	
	// Use filepath.Glob for basic patterns or walk for complex patterns
	if strings.Contains(pattern, "**") {
		matches, err = t.recursiveGlob(ctx, absSearchPath, pattern, includeDirs, includeHidden, excludePatterns, maxResults)
	} else {
		matches, err = t.simpleGlob(ctx, fullPattern, absSearchPath, includeDirs, includeHidden, excludePatterns, maxResults)
	}

	if err != nil {
		return nil, err
	}

	return matches, nil
}

// simpleGlob handles simple glob patterns
func (t *GlobTool) simpleGlob(ctx context.Context, pattern, basePath string, includeDirs, includeHidden bool, excludePatterns []string, maxResults int) ([]FileInfo, error) {
	paths, err := filepath.Glob(pattern)
	if err != nil {
		return nil, fmt.Errorf("invalid glob pattern: %w", err)
	}

	var matches []FileInfo
	for _, path := range paths {
		// Check context cancellation
		select {
		case <-ctx.Done():
			return matches, ctx.Err()
		default:
		}

		info, err := os.Stat(path)
		if err != nil {
			continue
		}

		// Apply filters
		if !t.shouldInclude(path, info, includeDirs, includeHidden, excludePatterns) {
			continue
		}

		fileInfo := t.createFileInfo(path, info, basePath)
		matches = append(matches, fileInfo)

		if len(matches) >= maxResults {
			break
		}
	}

	return matches, nil
}

// recursiveGlob handles recursive glob patterns with **
func (t *GlobTool) recursiveGlob(ctx context.Context, basePath, pattern string, includeDirs, includeHidden bool, excludePatterns []string, maxResults int) ([]FileInfo, error) {
	var matches []FileInfo
	
	// Remove ** from pattern and split
	parts := strings.Split(pattern, "**")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid recursive pattern: %s", pattern)
	}

	prefix := strings.TrimSuffix(parts[0], "/")
	suffix := strings.TrimPrefix(parts[1], "/")

	err := filepath.Walk(basePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // Skip files with errors
		}

		// Check context cancellation
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// Apply prefix filter
		if prefix != "" {
			relPath, _ := filepath.Rel(basePath, path)
			if !strings.HasPrefix(relPath, prefix) {
				if info.IsDir() {
					return filepath.SkipDir
				}
				return nil
			}
		}

		// Apply suffix filter
		if suffix != "" {
			matched, _ := filepath.Match(suffix, info.Name())
			if !matched {
				return nil
			}
		}

		// Apply filters
		if !t.shouldInclude(path, info, includeDirs, includeHidden, excludePatterns) {
			return nil
		}

		fileInfo := t.createFileInfo(path, info, basePath)
		matches = append(matches, fileInfo)

		if len(matches) >= maxResults {
			return fmt.Errorf("max results reached")
		}

		return nil
	})

	if err != nil && err.Error() != "max results reached" {
		return nil, err
	}

	return matches, nil
}

// shouldInclude determines if a file should be included in results
func (t *GlobTool) shouldInclude(path string, info os.FileInfo, includeDirs, includeHidden bool, excludePatterns []string) bool {
	// Check if it's a directory
	if info.IsDir() && !includeDirs {
		return false
	}

	// Check hidden files
	if !includeHidden && strings.HasPrefix(info.Name(), ".") {
		return false
	}

	// Check exclude patterns
	for _, excludePattern := range excludePatterns {
		if matched, _ := filepath.Match(excludePattern, info.Name()); matched {
			return false
		}
	}

	return true
}

// createFileInfo creates a FileInfo struct from os.FileInfo
func (t *GlobTool) createFileInfo(path string, info os.FileInfo, basePath string) FileInfo {
	relPath, _ := filepath.Rel(basePath, path)
	
	return FileInfo{
		Path:         path,
		Name:         info.Name(),
		Size:         info.Size(),
		Mode:         info.Mode().String(),
		ModTime:      info.ModTime(),
		IsDir:        info.IsDir(),
		Extension:    filepath.Ext(info.Name()),
		RelativePath: relPath,
	}
}

// sortMatches sorts the matches based on the specified criteria
func (t *GlobTool) sortMatches(matches []FileInfo, sortBy, sortOrder string) {
	sort.Slice(matches, func(i, j int) bool {
		var less bool
		
		switch sortBy {
		case "name":
			less = matches[i].Name < matches[j].Name
		case "size":
			less = matches[i].Size < matches[j].Size
		case "created":
			// Use ModTime as proxy for created time (not available in os.FileInfo)
			less = matches[i].ModTime.Before(matches[j].ModTime)
		case "modified":
		default:
			less = matches[i].ModTime.Before(matches[j].ModTime)
		}

		if sortOrder == "desc" {
			return !less
		}
		return less
	})
}
