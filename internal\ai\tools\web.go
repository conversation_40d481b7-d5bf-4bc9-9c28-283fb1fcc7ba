package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/arien-ai/cli/internal/ai/providers"
	"github.com/gocolly/colly/v2"
)

// WebTool implements web search functionality
type WebTool struct {
	*BaseTool
	client *http.Client
}

// NewWebTool creates a new web tool
func NewWebTool() *WebTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"query": map[string]interface{}{
				"type":        "string",
				"description": "Search query to find information on the web. Use specific keywords for better results.",
			},
			"max_results": map[string]interface{}{
				"type":        "number",
				"description": "Maximum number of search results to return. Defaults to 5, maximum 10.",
			},
			"search_type": map[string]interface{}{
				"type":        "string",
				"description": "Type of search: 'general', 'news', 'images'. Defaults to 'general'.",
				"enum":        []string{"general", "news", "images"},
			},
			"language": map[string]interface{}{
				"type":        "string",
				"description": "Language for search results (e.g., 'en', 'es', 'fr'). Defaults to 'en'.",
			},
			"region": map[string]interface{}{
				"type":        "string",
				"description": "Region for search results (e.g., 'us', 'uk', 'ca'). Defaults to 'us'.",
			},
			"time_range": map[string]interface{}{
				"type":        "string",
				"description": "Time range for results: 'any', 'day', 'week', 'month', 'year'. Defaults to 'any'.",
				"enum":        []string{"any", "day", "week", "month", "year"},
			},
			"safe_search": map[string]interface{}{
				"type":        "boolean",
				"description": "Enable safe search filtering. Defaults to true.",
			},
		},
		"required": []string{"query"},
	}

	description := `Web search tool for retrieving real-time and up-to-date information from the internet.

USAGE GUIDELINES:
- Searches the web using DuckDuckGo API for privacy-focused results
- Returns relevant web pages, news articles, and information
- Provides summaries and snippets from search results
- Supports different search types and filtering options
- Respects rate limits and implements proper error handling

WHEN TO USE:
- Getting current information, news, or recent developments
- Finding documentation, tutorials, or how-to guides
- Researching topics, technologies, or current events
- Verifying facts or getting multiple perspectives
- Finding specific websites, tools, or resources

WHEN NOT TO USE:
- For information that's already available locally
- For private or sensitive searches
- When offline or network connectivity is limited
- For very specific technical documentation (check local docs first)

SEARCH TYPES:
- 'general': Regular web search for any topic
- 'news': Recent news articles and current events
- 'images': Image search results (returns image URLs and descriptions)

EXAMPLES:
- General search: {"query": "Go 1.24 new features"}
- News search: {"query": "artificial intelligence", "search_type": "news", "time_range": "week"}
- Technical info: {"query": "Docker container best practices", "max_results": 3}
- Recent updates: {"query": "OpenAI GPT latest", "time_range": "month"}
- Regional search: {"query": "weather forecast", "region": "uk"}

SEARCH OPTIMIZATION:
- Use specific keywords for better results
- Include relevant technical terms or product names
- Use quotes for exact phrase matching
- Combine multiple keywords with AND/OR logic
- Specify time ranges for recent information

PRIVACY & SAFETY:
- Uses DuckDuckGo for privacy-focused searching
- Safe search enabled by default
- No tracking or personal data collection
- Respects website robots.txt and rate limits
- Returns only publicly available information`

	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	return &WebTool{
		BaseTool: NewBaseTool("web", description, parameters),
		client:   client,
	}
}

// Execute executes the web search
func (t *WebTool) Execute(ctx context.Context, args map[string]interface{}) (*providers.ToolResult, error) {
	// Validate arguments
	if err := t.ValidateArgs(args); err != nil {
		return providers.NewErrorToolResult("", err), nil
	}

	query, ok := args["query"].(string)
	if !ok {
		return providers.NewErrorToolResult("", fmt.Errorf("query must be a string")), nil
	}

	// Get optional parameters
	maxResults := 5
	if mr, ok := args["max_results"].(float64); ok {
		maxResults = int(mr)
		if maxResults > 10 {
			maxResults = 10
		}
	}

	searchType := "general"
	if st, ok := args["search_type"].(string); ok {
		searchType = st
	}

	language := "en"
	if l, ok := args["language"].(string); ok {
		language = l
	}

	region := "us"
	if r, ok := args["region"].(string); ok {
		region = r
	}

	timeRange := "any"
	if tr, ok := args["time_range"].(string); ok {
		timeRange = tr
	}

	safeSearch := true
	if ss, ok := args["safe_search"].(bool); ok {
		safeSearch = ss
	}

	// Perform web search
	results, err := t.performSearch(ctx, query, maxResults, searchType, language, region, timeRange, safeSearch)
	if err != nil {
		return providers.NewErrorToolResult("", err), nil
	}

	// Prepare result
	result := &providers.ToolResult{
		Success: true,
		Data: map[string]interface{}{
			"query":         query,
			"results_found": len(results),
			"results":       results,
			"search_params": map[string]interface{}{
				"max_results": maxResults,
				"search_type": searchType,
				"language":    language,
				"region":      region,
				"time_range":  timeRange,
				"safe_search": safeSearch,
			},
			"search_timestamp": time.Now().Format(time.RFC3339),
		},
	}

	if len(results) == 0 {
		result.Content = fmt.Sprintf("No results found for query '%s'", query)
	} else {
		result.Content = fmt.Sprintf("Found %d results for query '%s'", len(results), query)
	}

	return result, nil
}

// SearchResult represents a web search result
type SearchResult struct {
	Title       string    `json:"title"`
	URL         string    `json:"url"`
	Snippet     string    `json:"snippet"`
	Domain      string    `json:"domain"`
	PublishDate string    `json:"publish_date,omitempty"`
	ImageURL    string    `json:"image_url,omitempty"`
	Type        string    `json:"type"`
	Relevance   float64   `json:"relevance"`
	Timestamp   time.Time `json:"timestamp"`
}

// performSearch performs the actual web search
func (t *WebTool) performSearch(ctx context.Context, query string, maxResults int, searchType, language, region, timeRange string, safeSearch bool) ([]SearchResult, error) {
	switch searchType {
	case "general":
		return t.searchGeneral(ctx, query, maxResults, language, region, timeRange, safeSearch)
	case "news":
		return t.searchNews(ctx, query, maxResults, language, region, timeRange, safeSearch)
	case "images":
		return t.searchImages(ctx, query, maxResults, language, region, safeSearch)
	default:
		return nil, fmt.Errorf("unsupported search type: %s", searchType)
	}
}

// searchGeneral performs general web search using DuckDuckGo
func (t *WebTool) searchGeneral(ctx context.Context, query string, maxResults int, language, region, timeRange string, safeSearch bool) ([]SearchResult, error) {
	// Build DuckDuckGo search URL
	baseURL := "https://html.duckduckgo.com/html/"
	params := url.Values{}
	params.Set("q", query)
	params.Set("kl", region+"-"+language)
	
	if safeSearch {
		params.Set("safe", "strict")
	} else {
		params.Set("safe", "off")
	}

	// Add time range filter
	switch timeRange {
	case "day":
		params.Set("df", "d")
	case "week":
		params.Set("df", "w")
	case "month":
		params.Set("df", "m")
	case "year":
		params.Set("df", "y")
	}

	searchURL := baseURL + "?" + params.Encode()

	// Use colly for web scraping
	c := colly.NewCollector(
		colly.UserAgent("Mozilla/5.0 (compatible; ArienAI/1.0)"),
	)

	c.SetRequestTimeout(30 * time.Second)

	var results []SearchResult
	resultCount := 0

	// Extract search results
	c.OnHTML(".result", func(e *colly.HTMLElement) {
		if resultCount >= maxResults {
			return
		}

		title := e.ChildText(".result__title a")
		resultURL := e.ChildAttr(".result__title a", "href")
		snippet := e.ChildText(".result__snippet")

		if title != "" && resultURL != "" {
			// Parse domain from URL
			domain := ""
			if parsedURL, err := url.Parse(resultURL); err == nil {
				domain = parsedURL.Host
			}

			result := SearchResult{
				Title:     strings.TrimSpace(title),
				URL:       resultURL,
				Snippet:   strings.TrimSpace(snippet),
				Domain:    domain,
				Type:      "general",
				Relevance: 1.0 - (float64(resultCount) / float64(maxResults)),
				Timestamp: time.Now(),
			}

			results = append(results, result)
			resultCount++
		}
	})

	// Handle errors
	c.OnError(func(r *colly.Response, err error) {
		// Log error but continue
	})

	// Visit the search URL
	err := c.Visit(searchURL)
	if err != nil {
		return nil, fmt.Errorf("failed to perform search: %w", err)
	}

	return results, nil
}

// searchNews performs news search
func (t *WebTool) searchNews(ctx context.Context, query string, maxResults int, language, region, timeRange string, safeSearch bool) ([]SearchResult, error) {
	// Use DuckDuckGo news search
	baseURL := "https://duckduckgo.com/"
	params := url.Values{}
	params.Set("q", query)
	params.Set("iar", "news")
	params.Set("kl", region+"-"+language)

	if safeSearch {
		params.Set("safe", "strict")
	}

	// Add time range for news
	switch timeRange {
	case "day":
		params.Set("df", "d")
	case "week":
		params.Set("df", "w")
	case "month":
		params.Set("df", "m")
	}

	searchURL := baseURL + "?" + params.Encode()

	// Use colly for news scraping
	c := colly.NewCollector(
		colly.UserAgent("Mozilla/5.0 (compatible; ArienAI/1.0)"),
	)

	c.SetRequestTimeout(30 * time.Second)

	var results []SearchResult
	resultCount := 0

	// Extract news results
	c.OnHTML(".result--news", func(e *colly.HTMLElement) {
		if resultCount >= maxResults {
			return
		}

		title := e.ChildText(".result__title a")
		resultURL := e.ChildAttr(".result__title a", "href")
		snippet := e.ChildText(".result__snippet")
		publishDate := e.ChildText(".result__timestamp")

		if title != "" && resultURL != "" {
			domain := ""
			if parsedURL, err := url.Parse(resultURL); err == nil {
				domain = parsedURL.Host
			}

			result := SearchResult{
				Title:       strings.TrimSpace(title),
				URL:         resultURL,
				Snippet:     strings.TrimSpace(snippet),
				Domain:      domain,
				PublishDate: strings.TrimSpace(publishDate),
				Type:        "news",
				Relevance:   1.0 - (float64(resultCount) / float64(maxResults)),
				Timestamp:   time.Now(),
			}

			results = append(results, result)
			resultCount++
		}
	})

	err := c.Visit(searchURL)
	if err != nil {
		return nil, fmt.Errorf("failed to perform news search: %w", err)
	}

	return results, nil
}

// searchImages performs image search
func (t *WebTool) searchImages(ctx context.Context, query string, maxResults int, language, region string, safeSearch bool) ([]SearchResult, error) {
	// Use DuckDuckGo image search
	baseURL := "https://duckduckgo.com/"
	params := url.Values{}
	params.Set("q", query)
	params.Set("iax", "images")
	params.Set("ia", "images")
	params.Set("kl", region+"-"+language)

	if safeSearch {
		params.Set("safe", "strict")
	}

	searchURL := baseURL + "?" + params.Encode()

	// Use colly for image scraping
	c := colly.NewCollector(
		colly.UserAgent("Mozilla/5.0 (compatible; ArienAI/1.0)"),
	)

	c.SetRequestTimeout(30 * time.Second)

	var results []SearchResult
	resultCount := 0

	// Extract image results
	c.OnHTML(".tile--img", func(e *colly.HTMLElement) {
		if resultCount >= maxResults {
			return
		}

		imageURL := e.ChildAttr("img", "src")
		title := e.ChildAttr("img", "alt")
		sourceURL := e.ChildAttr("a", "href")

		if imageURL != "" {
			domain := ""
			if parsedURL, err := url.Parse(sourceURL); err == nil {
				domain = parsedURL.Host
			}

			result := SearchResult{
				Title:     strings.TrimSpace(title),
				URL:       sourceURL,
				ImageURL:  imageURL,
				Domain:    domain,
				Type:      "image",
				Relevance: 1.0 - (float64(resultCount) / float64(maxResults)),
				Timestamp: time.Now(),
			}

			results = append(results, result)
			resultCount++
		}
	})

	err := c.Visit(searchURL)
	if err != nil {
		return nil, fmt.Errorf("failed to perform image search: %w", err)
	}

	return results, nil
}

// GetSearchSuggestions gets search suggestions for a query
func (t *WebTool) GetSearchSuggestions(ctx context.Context, query string) ([]string, error) {
	// Use DuckDuckGo suggestions API
	suggestURL := fmt.Sprintf("https://duckduckgo.com/ac/?q=%s&type=list", url.QueryEscape(query))

	req, err := http.NewRequestWithContext(ctx, "GET", suggestURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (compatible; ArienAI/1.0)")

	resp, err := t.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var suggestions []map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&suggestions); err != nil {
		return nil, err
	}

	var result []string
	for _, suggestion := range suggestions {
		if phrase, ok := suggestion["phrase"].(string); ok {
			result = append(result, phrase)
		}
	}

	return result, nil
}
