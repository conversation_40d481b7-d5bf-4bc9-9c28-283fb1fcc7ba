# Arien AI CLI

A modern and powerful CLI terminal system powered by AI with LLM Function Tools calling and executing capabilities. Built with Go 1.24.0 and designed for intelligent task automation with a "Never Give Up" retry logic approach.

## 🚀 Features

### Core Capabilities
- **AI-Powered Intelligence**: Leverages advanced LLMs (Deepseek, Ollama) for intelligent command execution
- **Function Tools System**: 6 comprehensive tools for various operations
- **Real-time Streaming**: Live AI responses with beautiful animations
- **Never Give Up Logic**: Intelligent retry mechanisms with exponential backoff
- **Interactive CLI**: Modern terminal interface with real-time updates
- **Cross-Platform**: Supports Windows 11 WSL, macOS, and Linux

### AI Providers
- **Deepseek**: Models include `deepseek-chat` and `deepseek-reasoner`
- **Ollama**: Local AI model support for privacy and offline usage

### Available Tools

#### 🔧 Bash Tool
Execute system commands and shell operations with intelligent error handling.
```bash
# Examples the AI can execute:
- System monitoring: ps aux, df -h, top
- File operations: mkdir, cp, mv, rm
- Package management: apt install, brew install
- Process management: kill, killall
```

#### 🔍 Grep Tool
Fast content search across files with regex support and context.
```bash
# AI can search for:
- Function definitions: func\s+\w+\(
- Configuration values: database.*password
- Error patterns: ERROR|FATAL|CRITICAL
- Code patterns: import\s+.*react
```

#### 📁 Glob Tool
File pattern matching and discovery with advanced filtering.
```bash
# AI can find:
- Source files: **/*.go, **/*.js
- Configuration files: *config*, *.yaml
- Log files: **/*.log, **/logs/**
- Documentation: **/*.md, **/docs/**
```

#### ✏️ Write Tool
Create and update files with backup and permission management.
```bash
# AI can create:
- Configuration files with proper formatting
- Scripts with executable permissions
- Documentation and README files
- Data files with specific encoding
```

#### ✂️ Edit Tool
Precise file editing with regex support and change tracking.
```bash
# AI can perform:
- Text replacement with regex patterns
- Line insertion at specific positions
- Content deletion with backup
- Multi-line operations
```

#### 🌐 Web Tool
Real-time web search using DuckDuckGo for privacy-focused results.
```bash
# AI can search for:
- Latest documentation and tutorials
- Current news and developments
- Technical solutions and examples
- Package information and updates
```

## 📦 Installation

### Quick Install (Recommended)
```bash
curl -sSL https://raw.githubusercontent.com/arien-ai/cli/main/scripts/install.sh | bash
```

### Platform-Specific Installation

#### Windows 11 WSL
```bash
# Install in WSL environment
curl -sSL https://raw.githubusercontent.com/arien-ai/cli/main/scripts/install.sh | bash
```

#### macOS
```bash
# Using the universal installer
curl -sSL https://raw.githubusercontent.com/arien-ai/cli/main/scripts/install.sh | bash

# Or using Homebrew (if available)
brew install arien-ai/tap/arien
```

#### Linux
```bash
# Universal installer works on all Linux distributions
curl -sSL https://raw.githubusercontent.com/arien-ai/cli/main/scripts/install.sh | bash
```

### Manual Installation
1. Download the latest release from [GitHub Releases](https://github.com/arien-ai/cli/releases)
2. Extract the binary to `/usr/local/bin/arien`
3. Make it executable: `chmod +x /usr/local/bin/arien`
4. Run setup: `arien setup`

### Update Installation
```bash
# Update to latest version
curl -sSL https://raw.githubusercontent.com/arien-ai/cli/main/scripts/install.sh | bash -s -- --update
```

### Uninstall
```bash
# Complete removal
curl -sSL https://raw.githubusercontent.com/arien-ai/cli/main/scripts/install.sh | bash -s -- --uninstall
```

## ⚙️ Configuration

### Initial Setup
```bash
# Run the interactive setup wizard
arien setup

# Or initialize with default configuration
arien config init
```

### Configuration File
The configuration is stored at `~/.config/arien-ai/arien-ai.yaml`:

```yaml
ai:
  provider: deepseek  # or ollama
  deepseek:
    api_key: "your-deepseek-api-key"
    base_url: "https://api.deepseek.com"
    model: "deepseek-chat"
  ollama:
    host: "http://localhost:11434"
    model: "llama3.2"

cli:
  theme: default
  animation: true
  show_progress: true
  auto_approve: false
  max_history: 100

tools:
  bash:
    enabled: true
  grep:
    enabled: true
  glob:
    enabled: true
  write:
    enabled: true
  edit:
    enabled: true
  web:
    enabled: true

retry:
  max_attempts: 3
  initial_delay_ms: 1000
  max_delay_ms: 30000
  backoff_factor: 2.0
  enable_retry: true
```

### Environment Variables
```bash
# AI Provider Configuration
export ARIEN_AI_PROVIDER=deepseek
export ARIEN_AI_DEEPSEEK_API_KEY=your-api-key
export ARIEN_AI_OLLAMA_HOST=http://localhost:11434

# CLI Configuration
export ARIEN_AI_CLI_THEME=default
export ARIEN_AI_CLI_ANIMATION=true
```

## 🎯 Usage

### Interactive Mode
```bash
# Start interactive session
arien

# The AI will help you with various tasks:
arien > Find all Go files in this project and show their sizes
arien > Create a backup of my configuration files
arien > Search for recent Docker tutorials online
arien > Update all package.json files to use the latest React version
```

### Command Line Mode
```bash
# Direct commands
arien --provider deepseek "List all running processes"
arien --model deepseek-reasoner "Analyze this codebase structure"

# With specific tools
arien "Search for TODO comments in all JavaScript files"
arien "Download the latest Go documentation"
```

### Slash Commands
```bash
# Within interactive mode
/help                    # Show all commands
/provider deepseek       # Switch AI provider
/tools disable web       # Disable web search
/config show             # Display configuration
/history 10              # Show last 10 commands
/status                  # System status
/quit                    # Exit
```

## 🔄 Never Give Up Retry Logic

The system implements intelligent retry mechanisms:

- **Exponential Backoff**: Delays increase exponentially between retries
- **Smart Error Detection**: Distinguishes between retryable and permanent errors
- **Context Awareness**: Maintains operation context across retries
- **User Feedback**: Real-time progress updates during retry attempts

### Retryable Scenarios
- Network timeouts and connection issues
- Rate limiting (429 errors)
- Temporary server errors (5xx)
- Resource temporarily unavailable
- DNS resolution failures

## 🎨 User Interface Features

### Real-time Animations
```
( ●    ) AI is thinking... (3s)
⚙️ Processing request...
🔧 Executing: bash
✓ bash: Command executed successfully
```

### Progress Indicators
```
Downloading files [████████████████████████████████████████] 100% (5/5)
```

### Keyboard Shortcuts
- **Double ESC**: Interrupt ongoing operations
- **Ctrl+C**: Graceful exit
- **Tab**: Auto-completion (where supported)

## 🛠️ Development

### Building from Source
```bash
# Clone repository
git clone https://github.com/arien-ai/cli.git
cd cli

# Install dependencies
go mod download

# Build
go build -o arien cmd/arien/main.go

# Run tests
go test ./...
```

### Project Structure
```
arien-ai-cli/
├── cmd/arien/           # Main application entry point
├── internal/
│   ├── ai/              # AI provider implementations
│   │   ├── providers/   # Deepseek, Ollama providers
│   │   └── tools/       # Function tools (bash, grep, etc.)
│   ├── cli/             # CLI interface and commands
│   ├── config/          # Configuration management
│   └── utils/           # Utility functions
├── scripts/             # Installation and build scripts
└── docs/                # Documentation
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📚 Examples

### System Administration
```bash
arien > Check disk usage and clean up old log files
arien > Monitor system performance and identify bottlenecks
arien > Update all packages and restart necessary services
```

### Development Tasks
```bash
arien > Find all TODO comments and create a task list
arien > Refactor function names to follow camelCase convention
arien > Generate documentation for all public functions
```

### Data Processing
```bash
arien > Parse CSV files and convert to JSON format
arien > Find duplicate files and suggest cleanup actions
arien > Analyze log files for error patterns
```

## 🔒 Security & Privacy

- **Local Processing**: Ollama provider runs entirely locally
- **Privacy-First Web Search**: Uses DuckDuckGo for web searches
- **Secure Configuration**: API keys stored securely in user config
- **No Data Collection**: No telemetry or usage data sent to external services

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Support

- **Documentation**: [docs.arien-ai.com](https://docs.arien-ai.com)
- **Issues**: [GitHub Issues](https://github.com/arien-ai/cli/issues)
- **Discussions**: [GitHub Discussions](https://github.com/arien-ai/cli/discussions)
- **Discord**: [Join our community](https://discord.gg/arien-ai)

## 🙏 Acknowledgments

- Built with [Go 1.24.0](https://golang.org/)
- Powered by [Deepseek](https://deepseek.com/) and [Ollama](https://ollama.ai/)
- UI components from [Charm](https://charm.sh/)
- Web scraping with [Colly](https://go-colly.org/)

---

**Made with ❤️ by the Arien AI team**
