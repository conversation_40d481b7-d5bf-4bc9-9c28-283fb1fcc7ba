# Arien AI CLI Dockerfile
# Multi-stage build for optimal image size

# Build stage
FROM golang:1.24.0-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata \
    make

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download && go mod verify

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-w -s -X main.version=$(git describe --tags --always --dirty 2>/dev/null || echo 'dev') -X main.commit=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown') -X main.date=$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
    -o arien \
    cmd/arien/main.go

# Runtime stage
FROM alpine:3.19

# Install runtime dependencies
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    bash \
    curl \
    git \
    grep \
    findutils \
    coreutils \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S arien && \
    adduser -u 1001 -S arien -G arien

# Set working directory
WORKDIR /home/<USER>

# Copy binary from builder stage
COPY --from=builder /app/arien /usr/local/bin/arien

# Make binary executable
RUN chmod +x /usr/local/bin/arien

# Create config directory
RUN mkdir -p /home/<USER>/.config/arien-ai && \
    chown -R arien:arien /home/<USER>

# Switch to non-root user
USER arien

# Set environment variables
ENV ARIEN_AI_CONFIG_DIR=/home/<USER>/.config/arien-ai
ENV ARIEN_AI_CLI_ANIMATION=true
ENV ARIEN_AI_CLI_THEME=default

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD arien version || exit 1

# Default command
CMD ["arien"]

# Labels for metadata
LABEL maintainer="Arien AI Team <<EMAIL>>"
LABEL description="Arien AI CLI - Intelligent CLI Assistant"
LABEL version="1.0.0"
LABEL org.opencontainers.image.title="Arien AI CLI"
LABEL org.opencontainers.image.description="A modern and powerful CLI terminal system powered by AI"
LABEL org.opencontainers.image.url="https://github.com/arien-ai/cli"
LABEL org.opencontainers.image.documentation="https://docs.arien-ai.com"
LABEL org.opencontainers.image.source="https://github.com/arien-ai/cli"
LABEL org.opencontainers.image.vendor="Arien AI"
LABEL org.opencontainers.image.licenses="MIT"
