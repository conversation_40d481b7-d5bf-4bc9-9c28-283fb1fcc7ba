package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/arien-ai/cli/internal/ai/providers"
	"github.com/arien-ai/cli/internal/ai/tools"
	"github.com/arien-ai/cli/internal/cli"
	"github.com/arien-ai/cli/internal/config"
	"github.com/manifoldco/promptui"
	"github.com/spf13/cobra"
)

var (
	version = "1.0.0"
	commit  = "dev"
	date    = "unknown"
)

func main() {
	if err := newRootCommand().Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func newRootCommand() *cobra.Command {
	var configFile string
	var provider string
	var model string
	var verbose bool

	cmd := &cobra.Command{
		Use:   "arien",
		Short: "Arien AI - Intelligent CLI Assistant",
		Long: `Arien AI is a powerful CLI terminal system powered by AI with LLM Function Tools 
calling and executing capabilities. It provides intelligent assistance for various 
tasks using advanced AI models and a comprehensive set of tools.

Features:
- AI-powered command execution and assistance
- Multiple AI provider support (Deepseek, Ollama)
- Comprehensive tool system (bash, grep, glob, write, edit, web)
- Real-time streaming responses
- Never give up retry logic
- Interactive CLI interface with animations`,
		Version: fmt.Sprintf("%s (commit: %s, built: %s)", version, commit, date),
		RunE: func(cmd *cobra.Command, args []string) error {
			return runInteractiveMode(configFile, provider, model, verbose)
		},
	}

	// Global flags
	cmd.PersistentFlags().StringVarP(&configFile, "config", "c", "", "config file path")
	cmd.PersistentFlags().StringVarP(&provider, "provider", "p", "", "AI provider (deepseek, ollama)")
	cmd.PersistentFlags().StringVarP(&model, "model", "m", "", "AI model name")
	cmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "verbose output")

	// Add subcommands
	cmd.AddCommand(newConfigCommand())
	cmd.AddCommand(newToolsCommand())
	cmd.AddCommand(newVersionCommand())
	cmd.AddCommand(newSetupCommand())

	return cmd
}

func runInteractiveMode(configFile, provider, model string, verbose bool) error {
	// Load configuration
	cfg, err := loadConfig(configFile)
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	// Override config with command line flags
	if provider != "" {
		cfg.AI.Provider = provider
	}
	if model != "" {
		switch cfg.AI.Provider {
		case "deepseek":
			cfg.AI.Deepseek.Model = model
		case "ollama":
			cfg.AI.Ollama.Model = model
		}
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		return fmt.Errorf("invalid configuration: %w", err)
	}

	// Create AI provider
	aiProvider, err := createProvider(cfg)
	if err != nil {
		return fmt.Errorf("failed to create AI provider: %w", err)
	}

	// Create tool registry
	toolRegistry := tools.NewRegistry()

	// Create CLI interface
	cliInterface := cli.NewInterface(cfg, aiProvider, toolRegistry)

	// Setup context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Setup signal handling
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		cancel()
	}()

	// Start interactive mode
	return cliInterface.Start(ctx)
}

func loadConfig(configFile string) (*config.Config, error) {
	if configFile != "" {
		// Load from specific file
		// This would need custom implementation
		return config.Load()
	}
	
	// Load from default locations
	return config.Load()
}

func createProvider(cfg *config.Config) (providers.Provider, error) {
	// Use provider factory
	factory := providers.NewProviderFactory(cfg)
	return factory.CreateProvider(cfg.AI.Provider)
}

func newConfigCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "config",
		Short: "Configuration management",
		Long:  "Manage Arien AI configuration settings",
	}

	cmd.AddCommand(&cobra.Command{
		Use:   "show",
		Short: "Show current configuration",
		RunE: func(cmd *cobra.Command, args []string) error {
			cfg, err := config.Load()
			if err != nil {
				return err
			}
			
			fmt.Printf("Configuration:\n")
			fmt.Printf("  AI Provider: %s\n", cfg.AI.Provider)
			if cfg.AI.Provider == "deepseek" {
				fmt.Printf("  Deepseek Model: %s\n", cfg.AI.Deepseek.Model)
				fmt.Printf("  Deepseek Base URL: %s\n", cfg.AI.Deepseek.BaseURL)
			} else if cfg.AI.Provider == "ollama" {
				fmt.Printf("  Ollama Model: %s\n", cfg.AI.Ollama.Model)
				fmt.Printf("  Ollama Host: %s\n", cfg.AI.Ollama.Host)
			}
			
			return nil
		},
	})

	cmd.AddCommand(&cobra.Command{
		Use:   "init",
		Short: "Initialize configuration",
		RunE: func(cmd *cobra.Command, args []string) error {
			cfg := config.DefaultConfig()
			if err := cfg.Save(); err != nil {
				return fmt.Errorf("failed to save configuration: %w", err)
			}
			fmt.Println("Configuration initialized successfully")
			return nil
		},
	})

	return cmd
}

func newToolsCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "tools",
		Short: "Tool management",
		Long:  "Manage and inspect available tools",
	}

	cmd.AddCommand(&cobra.Command{
		Use:   "list",
		Short: "List all available tools",
		RunE: func(cmd *cobra.Command, args []string) error {
			registry := tools.NewRegistry()
			allTools := registry.GetAll()
			
			fmt.Println("Available Tools:")
			for name, tool := range allTools {
				status := "disabled"
				if tool.IsEnabled() {
					status = "enabled"
				}
				fmt.Printf("  %s - %s (%s)\n", name, tool.Name(), status)
			}
			
			return nil
		},
	})

	cmd.AddCommand(&cobra.Command{
		Use:   "info [tool-name]",
		Short: "Show detailed information about a tool",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			registry := tools.NewRegistry()
			tool, exists := registry.Get(args[0])
			if !exists {
				return fmt.Errorf("tool not found: %s", args[0])
			}
			
			fmt.Printf("Tool: %s\n", tool.Name())
			fmt.Printf("Description: %s\n", tool.Description())
			fmt.Printf("Enabled: %t\n", tool.IsEnabled())
			
			return nil
		},
	})

	return cmd
}

func newVersionCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "version",
		Short: "Show version information",
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Printf("Arien AI CLI\n")
			fmt.Printf("Version: %s\n", version)
			fmt.Printf("Commit: %s\n", commit)
			fmt.Printf("Built: %s\n", date)
			fmt.Printf("Go Version: %s\n", "1.24.0")
		},
	}
}

func newSetupCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "setup",
		Short: "Interactive setup wizard",
		Long:  "Run the interactive setup wizard to configure Arien AI",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runSetupWizard()
		},
	}

	return cmd
}

func runSetupWizard() error {
	fmt.Println("🚀 Welcome to Arien AI Setup Wizard!")
	fmt.Println("=====================================")
	fmt.Println()

	// Load or create default config
	cfg := config.DefaultConfig()

	// Provider selection
	providerPrompt := promptui.Select{
		Label: "Select AI Provider",
		Items: []string{"deepseek", "ollama"},
		Templates: &promptui.SelectTemplates{
			Label:    "{{ . }}?",
			Active:   "\U0001F449 {{ . | cyan }}",
			Inactive: "  {{ . | white }}",
			Selected: "\U0001F44D {{ . | green }}",
		},
	}

	_, provider, err := providerPrompt.Run()
	if err != nil {
		return fmt.Errorf("provider selection failed: %w", err)
	}

	cfg.AI.Provider = provider
	fmt.Printf("Selected provider: %s\n\n", provider)

	// Configure selected provider
	switch provider {
	case "deepseek":
		if err := setupDeepseek(cfg); err != nil {
			return err
		}
	case "ollama":
		if err := setupOllama(cfg); err != nil {
			return err
		}
	}

	// Save configuration
	if err := cfg.Save(); err != nil {
		return fmt.Errorf("failed to save configuration: %w", err)
	}

	fmt.Println()
	fmt.Println("✅ Setup completed successfully!")
	fmt.Println()
	fmt.Println("Next steps:")
	fmt.Println("  1. Run 'arien' to start the interactive CLI")
	fmt.Println("  2. Type '/help' for available commands")
	fmt.Println("  3. Try asking: 'List files in current directory'")
	fmt.Println()

	return nil
}

func setupDeepseek(cfg *config.Config) error {
	fmt.Println("🔧 Configuring Deepseek Provider")
	fmt.Println("Get your API key from: https://platform.deepseek.com/")
	fmt.Println()

	// API Key
	apiKeyPrompt := promptui.Prompt{
		Label: "Enter your Deepseek API key",
		Mask:  '*',
		Validate: func(input string) error {
			if len(input) < 10 {
				return fmt.Errorf("API key seems too short")
			}
			return nil
		},
	}

	apiKey, err := apiKeyPrompt.Run()
	if err != nil {
		return fmt.Errorf("API key input failed: %w", err)
	}

	cfg.AI.Deepseek.APIKey = apiKey

	// Model selection
	modelPrompt := promptui.Select{
		Label: "Select Deepseek model",
		Items: []string{"deepseek-chat", "deepseek-reasoner"},
		Templates: &promptui.SelectTemplates{
			Label:    "{{ . }}?",
			Active:   "\U0001F449 {{ . | cyan }}",
			Inactive: "  {{ . | white }}",
			Selected: "\U0001F44D {{ . | green }}",
		},
	}

	_, model, err := modelPrompt.Run()
	if err != nil {
		return fmt.Errorf("model selection failed: %w", err)
	}

	cfg.AI.Deepseek.Model = model

	// Test connection
	fmt.Println("\n🔍 Testing connection...")
	factory := providers.NewProviderFactory(cfg)
	if err := factory.TestProvider("deepseek"); err != nil {
		fmt.Printf("⚠️  Connection test failed: %v\n", err)
		fmt.Println("You can continue setup and fix the configuration later.")
	} else {
		fmt.Println("✅ Connection test successful!")
	}

	return nil
}

func setupOllama(cfg *config.Config) error {
	fmt.Println("🔧 Configuring Ollama Provider")
	fmt.Println("Make sure Ollama is running on your system.")
	fmt.Println("Install from: https://ollama.ai/")
	fmt.Println()

	// Host
	hostPrompt := promptui.Prompt{
		Label:   "Ollama host",
		Default: "http://localhost:11434",
	}

	host, err := hostPrompt.Run()
	if err != nil {
		return fmt.Errorf("host input failed: %w", err)
	}

	cfg.AI.Ollama.Host = host

	// Model
	modelPrompt := promptui.Prompt{
		Label:   "Model name",
		Default: "llama3.2",
		Validate: func(input string) error {
			if input == "" {
				return fmt.Errorf("model name cannot be empty")
			}
			return nil
		},
	}

	model, err := modelPrompt.Run()
	if err != nil {
		return fmt.Errorf("model input failed: %w", err)
	}

	cfg.AI.Ollama.Model = model

	// Test connection
	fmt.Println("\n🔍 Testing connection...")
	factory := providers.NewProviderFactory(cfg)
	if err := factory.TestProvider("ollama"); err != nil {
		fmt.Printf("⚠️  Connection test failed: %v\n", err)
		fmt.Println("Make sure Ollama is running and the model is available.")
		fmt.Printf("Run: ollama pull %s\n", model)
	} else {
		fmt.Println("✅ Connection test successful!")
	}

	return nil
}
