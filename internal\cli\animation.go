package cli

import (
	"fmt"
	"time"

	"github.com/fatih/color"
)

// AnimationManager handles loading animations and progress indicators
type AnimationManager struct {
	isRunning bool
	stopChan  chan bool
	frames    []string
	interval  time.Duration
}

// NewAnimationManager creates a new animation manager
func NewAnimationManager() *AnimationManager {
	// Custom ball animation frames as specified
	ballFrames := []string{
		"( ●    )",
		"(  ●   )",
		"(   ●  )",
		"(    ● )",
		"(     ●)",
		"(    ● )",
		"(   ●  )",
		"(  ●   )",
		"( ●    )",
		"(●     )",
	}

	return &AnimationManager{
		frames:   ballFrames,
		interval: 100 * time.Millisecond,
		stopChan: make(chan bool),
	}
}

// Start starts the animation with a message
func (a *AnimationManager) Start(message string) {
	if a.isRunning {
		return
	}

	a.isRunning = true
	go a.animate(message)
}

// Stop stops the animation
func (a *AnimationManager) Stop() {
	if !a.isRunning {
		return
	}

	a.isRunning = false
	a.stopChan <- true
	
	// Clear the animation line
	fmt.Print("\r\033[K")
}

// animate runs the animation loop
func (a *AnimationManager) animate(message string) {
	frameIndex := 0
	startTime := time.Now()
	
	ticker := time.NewTicker(a.interval)
	defer ticker.Stop()

	for {
		select {
		case <-a.stopChan:
			return
		case <-ticker.C:
			elapsed := time.Since(startTime)
			elapsedSeconds := int(elapsed.Seconds())
			
			// Create the animation frame with elapsed time
			frame := a.frames[frameIndex]
			animatedMessage := fmt.Sprintf("\r%s %s (%ds)", 
				color.CyanString(frame), 
				message, 
				elapsedSeconds)
			
			fmt.Print(animatedMessage)
			
			frameIndex = (frameIndex + 1) % len(a.frames)
		}
	}
}

// ShowProgress displays a progress bar
func ShowProgress(current, total int, message string) {
	if total == 0 {
		return
	}

	percentage := float64(current) / float64(total) * 100
	barLength := 40
	filledLength := int(float64(barLength) * percentage / 100)

	bar := ""
	for i := 0; i < barLength; i++ {
		if i < filledLength {
			bar += "█"
		} else {
			bar += "░"
		}
	}

	progressText := fmt.Sprintf("\r%s [%s] %.1f%% (%d/%d)", 
		message, 
		color.GreenString(bar), 
		percentage, 
		current, 
		total)
	
	fmt.Print(progressText)
	
	if current == total {
		fmt.Println() // New line when complete
	}
}

// ShowSpinner displays a simple spinner
func ShowSpinner(message string, duration time.Duration) {
	spinnerFrames := []string{"⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"}
	
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()
	
	timeout := time.After(duration)
	frameIndex := 0
	
	for {
		select {
		case <-timeout:
			fmt.Print("\r\033[K") // Clear line
			return
		case <-ticker.C:
			frame := spinnerFrames[frameIndex]
			fmt.Printf("\r%s %s", color.CyanString(frame), message)
			frameIndex = (frameIndex + 1) % len(spinnerFrames)
		}
	}
}

// ShowSuccess displays a success message
func ShowSuccess(message string) {
	fmt.Printf("%s %s\n", color.GreenString("✓"), message)
}

// ShowError displays an error message
func ShowError(message string) {
	fmt.Printf("%s %s\n", color.RedString("✗"), message)
}

// ShowWarning displays a warning message
func ShowWarning(message string) {
	fmt.Printf("%s %s\n", color.YellowString("⚠"), message)
}

// ShowInfo displays an info message
func ShowInfo(message string) {
	fmt.Printf("%s %s\n", color.BlueString("ℹ"), message)
}

// ShowThinking displays a thinking animation
func ShowThinking(message string) *AnimationManager {
	thinkingFrames := []string{
		"🤔   ",
		" 🤔  ",
		"  🤔 ",
		"   🤔",
		"  🤔 ",
		" 🤔  ",
	}
	
	anim := &AnimationManager{
		frames:   thinkingFrames,
		interval: 200 * time.Millisecond,
		stopChan: make(chan bool),
	}
	
	anim.Start(message)
	return anim
}

// ShowTyping displays a typing animation
func ShowTyping(message string) *AnimationManager {
	typingFrames := []string{
		"⌨️ ",
		"⌨️.",
		"⌨️..",
		"⌨️...",
		"⌨️ ",
	}
	
	anim := &AnimationManager{
		frames:   typingFrames,
		interval: 300 * time.Millisecond,
		stopChan: make(chan bool),
	}
	
	anim.Start(message)
	return anim
}

// ShowProcessing displays a processing animation
func ShowProcessing(message string) *AnimationManager {
	processingFrames := []string{
		"⚙️ ",
		"⚙️ ",
		"⚙️ ",
		"⚙️ ",
	}
	
	anim := &AnimationManager{
		frames:   processingFrames,
		interval: 150 * time.Millisecond,
		stopChan: make(chan bool),
	}
	
	anim.Start(message)
	return anim
}

// ClearLine clears the current line
func ClearLine() {
	fmt.Print("\r\033[K")
}

// MoveCursorUp moves cursor up by n lines
func MoveCursorUp(n int) {
	fmt.Printf("\033[%dA", n)
}

// MoveCursorDown moves cursor down by n lines
func MoveCursorDown(n int) {
	fmt.Printf("\033[%dB", n)
}

// SaveCursorPosition saves the current cursor position
func SaveCursorPosition() {
	fmt.Print("\033[s")
}

// RestoreCursorPosition restores the saved cursor position
func RestoreCursorPosition() {
	fmt.Print("\033[u")
}

// HideCursor hides the cursor
func HideCursor() {
	fmt.Print("\033[?25l")
}

// ShowCursor shows the cursor
func ShowCursor() {
	fmt.Print("\033[?25h")
}

// ColoredText returns colored text based on type
func ColoredText(text, textType string) string {
	switch textType {
	case "success":
		return color.GreenString(text)
	case "error":
		return color.RedString(text)
	case "warning":
		return color.YellowString(text)
	case "info":
		return color.BlueString(text)
	case "highlight":
		return color.CyanString(text)
	case "muted":
		return color.New(color.FgHiBlack).Sprint(text)
	case "bold":
		return color.New(color.Bold).Sprint(text)
	default:
		return text
	}
}

// PrintBanner prints a styled banner
func PrintBanner(title, subtitle string) {
	banner := fmt.Sprintf(`
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║  %s                                                                          ║
║  %s                                                                          ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
`, 
		color.CyanString("%-76s", title),
		color.New(color.FgHiBlack).Sprintf("%-76s", subtitle))
	
	fmt.Print(banner)
}

// PrintSeparator prints a separator line
func PrintSeparator(char string, length int) {
	separator := ""
	for i := 0; i < length; i++ {
		separator += char
	}
	fmt.Println(color.New(color.FgHiBlack).Sprint(separator))
}
