package tools

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"time"

	"github.com/arien-ai/cli/internal/ai/providers"
)

// BashTool implements bash command execution
type BashTool struct {
	*BaseTool
}

// NewBashTool creates a new bash tool
func NewBashTool() *BashTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"command": map[string]interface{}{
				"type":        "string",
				"description": "The bash command to execute. Use proper shell syntax and escape special characters.",
			},
			"working_directory": map[string]interface{}{
				"type":        "string",
				"description": "Optional working directory for the command. Defaults to current directory.",
			},
			"timeout": map[string]interface{}{
				"type":        "number",
				"description": "Optional timeout in seconds. Defaults to 30 seconds.",
			},
			"capture_output": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether to capture command output. Defaults to true.",
			},
		},
		"required": []string{"command"},
	}

	description := `Executes bash commands in an interactive shell environment.

USAGE GUIDELINES:
- Use this tool for running system commands, file operations, and shell scripts
- Commands run in the current working directory unless working_directory is specified
- Output is captured and returned in JSON format (not shown in CLI interface)
- Use proper shell syntax and escape special characters
- For complex operations, break them into multiple commands
- Always validate file paths and permissions before operations

WHEN TO USE:
- Running system utilities (ls, ps, df, etc.)
- File and directory operations (mkdir, cp, mv, rm, etc.)
- Process management and system monitoring
- Installing packages or running build commands
- Executing scripts and automation tasks

WHEN NOT TO USE:
- For simple file reading (use grep tool instead)
- For file pattern matching (use glob tool instead)
- For file writing (use write tool instead)
- For interactive commands that require user input

EXAMPLES:
- List files: {"command": "ls -la"}
- Create directory: {"command": "mkdir -p /path/to/dir"}
- Check disk space: {"command": "df -h"}
- Run with timeout: {"command": "long_running_command", "timeout": 60}

SAFETY NOTES:
- Commands are executed with current user permissions
- Be careful with destructive operations (rm, mv, etc.)
- Avoid commands that require interactive input
- Use absolute paths when possible for clarity`

	return &BashTool{
		BaseTool: NewBaseTool("bash", description, parameters),
	}
}

// Execute executes the bash command
func (t *BashTool) Execute(ctx context.Context, args map[string]interface{}) (*providers.ToolResult, error) {
	// Validate arguments
	if err := t.ValidateArgs(args); err != nil {
		return providers.NewErrorToolResult("", err), nil
	}

	command, ok := args["command"].(string)
	if !ok {
		return providers.NewErrorToolResult("", fmt.Errorf("command must be a string")), nil
	}

	// Get optional parameters
	workingDir := ""
	if wd, ok := args["working_directory"].(string); ok {
		workingDir = wd
	}

	timeout := 30 * time.Second
	if t, ok := args["timeout"].(float64); ok {
		timeout = time.Duration(t) * time.Second
	}

	captureOutput := true
	if co, ok := args["capture_output"].(bool); ok {
		captureOutput = co
	}

	// Create context with timeout
	execCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// Determine shell based on OS
	var cmd *exec.Cmd
	switch runtime.GOOS {
	case "windows":
		cmd = exec.CommandContext(execCtx, "powershell", "-Command", command)
	default:
		cmd = exec.CommandContext(execCtx, "bash", "-c", command)
	}

	// Set working directory if specified
	if workingDir != "" {
		if _, err := os.Stat(workingDir); os.IsNotExist(err) {
			return providers.NewErrorToolResult("", fmt.Errorf("working directory does not exist: %s", workingDir)), nil
		}
		cmd.Dir = workingDir
	}

	// Execute command
	var output []byte
	var err error

	if captureOutput {
		output, err = cmd.CombinedOutput()
	} else {
		err = cmd.Run()
		output = []byte("Command executed without output capture")
	}

	// Prepare result
	result := &providers.ToolResult{
		Success: err == nil,
		Data: map[string]interface{}{
			"command":           command,
			"working_directory": workingDir,
			"exit_code":         cmd.ProcessState.ExitCode(),
			"output":            string(output),
			"execution_time":    time.Since(time.Now().Add(-timeout)).String(),
		},
	}

	if err != nil {
		result.Error = err.Error()
		result.Content = fmt.Sprintf("Command failed: %s\nOutput: %s", err.Error(), string(output))
	} else {
		result.Content = fmt.Sprintf("Command executed successfully\nOutput: %s", string(output))
	}

	return result, nil
}

// GetShellInfo returns information about the current shell environment
func GetShellInfo() map[string]string {
	info := make(map[string]string)
	
	info["os"] = runtime.GOOS
	info["arch"] = runtime.GOARCH
	
	if runtime.GOOS == "windows" {
		info["shell"] = "powershell"
	} else {
		info["shell"] = "bash"
	}
	
	if wd, err := os.Getwd(); err == nil {
		info["working_directory"] = wd
	}
	
	if user := os.Getenv("USER"); user != "" {
		info["user"] = user
	} else if user := os.Getenv("USERNAME"); user != "" {
		info["user"] = user
	}
	
	if home := os.Getenv("HOME"); home != "" {
		info["home"] = home
	} else if home := os.Getenv("USERPROFILE"); home != "" {
		info["home"] = home
	}
	
	return info
}

// ValidateCommand performs basic validation on bash commands
func ValidateCommand(command string) error {
	command = strings.TrimSpace(command)
	
	if command == "" {
		return fmt.Errorf("command cannot be empty")
	}
	
	// Check for potentially dangerous commands
	dangerous := []string{
		"rm -rf /",
		":(){ :|:& };:",  // Fork bomb
		"dd if=/dev/zero", // Disk fill
		"chmod -R 777 /", // Dangerous permissions
	}
	
	for _, danger := range dangerous {
		if strings.Contains(strings.ToLower(command), danger) {
			return fmt.Errorf("potentially dangerous command detected: %s", danger)
		}
	}
	
	return nil
}

// SanitizeCommand sanitizes a command for safe execution
func SanitizeCommand(command string) string {
	// Remove potentially dangerous characters and sequences
	command = strings.ReplaceAll(command, "$(", "\\$(")
	command = strings.ReplaceAll(command, "`", "\\`")
	
	return command
}
