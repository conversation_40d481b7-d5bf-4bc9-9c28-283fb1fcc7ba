# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directories
build/
dist/
bin/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Configuration files with secrets
*.env
.env.*
config.yaml
config.yml
arien-ai.yaml
arien-ai.yml

# Log files
*.log
logs/

# Temporary files
tmp/
temp/
*.tmp

# Coverage files
coverage.out
coverage.html
coverage.xml

# Profiling files
*.prof
*.pprof

# Documentation build
docs/_build/
docs/site/

# Node.js (if using for docs or tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (if using for scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Docker
.dockerignore

# Kubernetes
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Local development
.local/
.cache/

# Test artifacts
testdata/output/
test-results/

# Backup files
*.backup
*.bak

# Archive files
*.tar.gz
*.zip
*.rar

# Database files
*.db
*.sqlite
*.sqlite3

# Certificate files
*.pem
*.key
*.crt
*.p12

# API keys and secrets
secrets.txt
api-keys.txt
.secrets/

# Release artifacts
releases/
packages/

# Vendor directory (uncomment if using)
# vendor/

# Go module cache
go.sum.backup

# Editor specific
.vimrc.local
.nvimrc.local

# JetBrains
.idea/
*.iml
*.iws
*.ipr

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Sublime Text
*.sublime-project
*.sublime-workspace

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]
Session.vim
.netrwhist

# Local configuration override
local.yaml
local.yml
override.yaml
override.yml

# Development scripts
dev-*.sh
test-*.sh
local-*.sh

# Performance and debugging
*.trace
*.heap
*.cpu

# Documentation
docs/build/
site/

# CI/CD
.github/workflows/local-*
.gitlab-ci-local.yml

# Miscellaneous
.scratch/
scratch/
playground/
experiments/
