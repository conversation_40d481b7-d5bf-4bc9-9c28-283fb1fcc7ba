package cli

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"

	"github.com/arien-ai/cli/internal/ai"
	"github.com/arien-ai/cli/internal/ai/providers"
	"github.com/arien-ai/cli/internal/ai/tools"
	"github.com/arien-ai/cli/internal/config"
	"github.com/fatih/color"
	"golang.org/x/term"
)

// Interface represents the CLI interface
type Interface struct {
	config       *config.Config
	provider     providers.Provider
	toolRegistry *tools.Registry
	processor    *ai.Processor
	animation    *AnimationManager
	history      []string
	isRunning    bool
	interrupted  bool
	lastResult   *ai.ProcessResult
}

// NewInterface creates a new CLI interface
func NewInterface(cfg *config.Config, provider providers.Provider, toolRegistry *tools.Registry) *Interface {
	processor := ai.NewProcessor(provider, toolRegistry, cfg)

	return &Interface{
		config:       cfg,
		provider:     provider,
		toolRegistry: toolRegistry,
		processor:    processor,
		animation:    NewAnimationManager(),
		history:      make([]string, 0, cfg.CLI.MaxHistory),
		isRunning:    false,
	}
}

// Start starts the interactive CLI
func (i *Interface) Start(ctx context.Context) error {
	// Print welcome banner
	i.printWelcomeBanner()

	// Setup signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Setup double ESC detection for interruption
	escChan := make(chan bool, 1)
	go i.detectDoubleEscape(escChan)

	i.isRunning = true

	// Main interaction loop
	for i.isRunning {
		select {
		case <-ctx.Done():
			i.isRunning = false
			return ctx.Err()
		case <-sigChan:
			i.handleShutdown()
			return nil
		case <-escChan:
			i.handleInterruption()
			continue
		default:
			if err := i.handleUserInput(ctx); err != nil {
				ShowError(fmt.Sprintf("Error: %v", err))
			}
		}
	}

	return nil
}

// printWelcomeBanner prints the welcome banner
func (i *Interface) printWelcomeBanner() {
	title := "ARIEN AI - Intelligent CLI Assistant"
	subtitle := fmt.Sprintf("Provider: %s | Model: %s | Tools: %d available", 
		i.provider.Name(), 
		i.getModelName(), 
		len(i.toolRegistry.GetEnabled()))
	
	PrintBanner(title, subtitle)
	
	fmt.Println()
	ShowInfo("Welcome to Arien AI! Type your message or use slash commands.")
	ShowInfo("Double-tap ESC to interrupt ongoing operations.")
	ShowInfo("Type '/help' for available commands or '/quit' to exit.")
	fmt.Println()
}

// handleUserInput handles user input and processes commands
func (i *Interface) handleUserInput(ctx context.Context) error {
	// Show prompt
	i.showPrompt()

	// Read user input
	input, err := i.readInput()
	if err != nil {
		return err
	}

	input = strings.TrimSpace(input)
	if input == "" {
		return nil
	}

	// Add to history
	i.addToHistory(input)

	// Handle slash commands
	if strings.HasPrefix(input, "/") {
		return i.handleSlashCommand(ctx, input)
	}

	// Process AI request
	return i.processAIRequest(ctx, input)
}

// showPrompt shows the input prompt
func (i *Interface) showPrompt() {
	prompt := color.CyanString("arien-ai") + color.New(color.FgHiBlack).Sprint(" > ")
	fmt.Print(prompt)
}

// readInput reads user input with support for multi-line input
func (i *Interface) readInput() (string, error) {
	reader := bufio.NewReader(os.Stdin)
	
	// Check if input is from terminal for better handling
	if term.IsTerminal(int(os.Stdin.Fd())) {
		// Terminal input - handle normally
		input, err := reader.ReadString('\n')
		if err != nil {
			return "", err
		}
		return strings.TrimSuffix(input, "\n"), nil
	} else {
		// Pipe input - read all
		input, err := reader.ReadString('\n')
		if err != nil {
			return "", err
		}
		return strings.TrimSuffix(input, "\n"), nil
	}
}

// processAIRequest processes an AI request
func (i *Interface) processAIRequest(ctx context.Context, input string) error {
	// Show thinking animation
	thinking := ShowThinking("AI is thinking...")
	defer thinking.Stop()

	// Prepare messages
	messages := []providers.Message{
		{
			Role:    "system",
			Content: i.getSystemPrompt(),
		},
		{
			Role:    "user",
			Content: input,
		},
	}

	// Prepare tools
	availableTools := i.toolRegistry.ToProviderTools()

	// Create chat request
	request := &providers.ChatRequest{
		Messages:    messages,
		Tools:       availableTools,
		ToolChoice:  "auto",
		Temperature: 0.7,
		MaxTokens:   4000,
		Stream:      true,
	}

	// Stop thinking animation
	thinking.Stop()

	// Show processing animation
	processing := ShowProcessing("Processing request...")

	// Send request to AI provider
	responseChan, err := i.provider.ChatStream(ctx, request)
	if err != nil {
		processing.Stop()
		return fmt.Errorf("failed to send request: %w", err)
	}

	processing.Stop()
	fmt.Println()

	// Process streaming response
	return i.handleStreamingResponse(ctx, responseChan, messages)
}

// handleStreamingResponse handles streaming AI response
func (i *Interface) handleStreamingResponse(ctx context.Context, responseChan <-chan *providers.ChatStreamResponse, messages []providers.Message) error {
	var fullResponse strings.Builder
	var toolCalls []providers.ToolCall
	
	ShowInfo("AI Response:")
	fmt.Println()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case response, ok := <-responseChan:
			if !ok {
				// Channel closed
				break
			}

			if response.Error != "" {
				ShowError(fmt.Sprintf("AI Error: %s", response.Error))
				return fmt.Errorf("AI error: %s", response.Error)
			}

			if len(response.Choices) > 0 {
				choice := response.Choices[0]
				
				// Handle content
				if choice.Delta.Content != "" {
					fmt.Print(choice.Delta.Content)
					fullResponse.WriteString(choice.Delta.Content)
				}

				// Handle tool calls
				if len(choice.Delta.ToolCalls) > 0 {
					toolCalls = append(toolCalls, choice.Delta.ToolCalls...)
				}

				// Check if response is complete
				if choice.FinishReason == "tool_calls" {
					fmt.Println()
					return i.handleToolCalls(ctx, toolCalls, messages, fullResponse.String())
				} else if choice.FinishReason == "stop" {
					fmt.Println()
					fmt.Println()
					return nil
				}
			}

			if response.Done {
				fmt.Println()
				fmt.Println()
				break
			}
		}
	}

	return nil
}

// handleToolCalls handles tool execution
func (i *Interface) handleToolCalls(ctx context.Context, toolCalls []providers.ToolCall, messages []providers.Message, aiResponse string) error {
	if len(toolCalls) == 0 {
		return nil
	}

	ShowInfo(fmt.Sprintf("Executing %d tool(s)...", len(toolCalls)))
	fmt.Println()

	// Execute tools
	var toolResults []providers.ToolResult
	for _, toolCall := range toolCalls {
		// Show tool execution
		ShowInfo(fmt.Sprintf("🔧 Executing: %s", toolCall.Function.Name))
		
		// Execute tool
		result, err := i.toolRegistry.ExecuteToolCall(ctx, toolCall)
		if err != nil {
			ShowError(fmt.Sprintf("Tool execution failed: %v", err))
			result = providers.NewErrorToolResult(toolCall.ID, err)
		}

		toolResults = append(toolResults, *result)

		// Show result (without raw output)
		if result.Success {
			ShowSuccess(fmt.Sprintf("✓ %s: %s", toolCall.Function.Name, result.Content))
		} else {
			ShowError(fmt.Sprintf("✗ %s: %s", toolCall.Function.Name, result.Content))
		}
	}

	fmt.Println()

	// Continue conversation with tool results
	return i.continueWithToolResults(ctx, messages, aiResponse, toolCalls, toolResults)
}

// continueWithToolResults continues the conversation with tool results
func (i *Interface) continueWithToolResults(ctx context.Context, messages []providers.Message, aiResponse string, toolCalls []providers.ToolCall, toolResults []providers.ToolResult) error {
	// Add assistant message with tool calls
	assistantMessage := providers.Message{
		Role:      "assistant",
		Content:   aiResponse,
		ToolCalls: toolCalls,
	}
	messages = append(messages, assistantMessage)

	// Add tool result messages
	for _, result := range toolResults {
		toolMessage := providers.Message{
			Role:    "tool",
			Content: providers.FormatToolResult(&result),
		}
		messages = append(messages, toolMessage)
	}

	// Send follow-up request
	request := &providers.ChatRequest{
		Messages:    messages,
		Tools:       i.toolRegistry.ToProviderTools(),
		ToolChoice:  "auto",
		Temperature: 0.7,
		MaxTokens:   4000,
		Stream:      true,
	}

	responseChan, err := i.provider.ChatStream(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to send follow-up request: %w", err)
	}

	ShowInfo("AI Analysis:")
	fmt.Println()

	return i.handleStreamingResponse(ctx, responseChan, messages)
}

// detectDoubleEscape detects double ESC key press
func (i *Interface) detectDoubleEscape(escChan chan bool) {
	// This is a simplified implementation
	// In a real implementation, you'd need proper terminal handling
	// For now, we'll use a different approach
}

// handleInterruption handles user interruption
func (i *Interface) handleInterruption() {
	if !i.interrupted {
		ShowWarning("Operation interrupted by user")
		i.interrupted = true
		// Stop any ongoing animations
		i.animation.Stop()
	}
}

// handleShutdown handles graceful shutdown
func (i *Interface) handleShutdown() {
	fmt.Println()
	ShowInfo("Shutting down Arien AI...")
	i.animation.Stop()
	i.isRunning = false
}

// addToHistory adds input to command history
func (i *Interface) addToHistory(input string) {
	i.history = append(i.history, input)
	if len(i.history) > i.config.CLI.MaxHistory {
		i.history = i.history[1:]
	}
}

// getSystemPrompt returns the system prompt for the AI
func (i *Interface) getSystemPrompt() string {
	if deepseekProvider, ok := i.provider.(*providers.DeepseekProvider); ok {
		return deepseekProvider.GetSystemPrompt()
	}
	if ollamaProvider, ok := i.provider.(*providers.OllamaProvider); ok {
		return ollamaProvider.GetSystemPrompt()
	}
	
	// Default system prompt
	return `You are Arien AI, a powerful AI assistant with access to various tools. 
Always respond with tool outputs in JSON format only. 
Never show raw tool outputs in the CLI interface.
Think carefully about which tools to use and provide helpful responses.`
}

// getModelName returns the current model name
func (i *Interface) getModelName() string {
	switch i.provider.Name() {
	case "deepseek":
		return i.config.AI.Deepseek.Model
	case "ollama":
		return i.config.AI.Ollama.Model
	default:
		return "unknown"
	}
}


