package providers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/ollama/ollama/api"
)

// OllamaProvider implements the Provider interface for Ollama
type OllamaProvider struct {
	client *api.Client
	host   string
	model  string
}

// NewOllamaProvider creates a new Ollama provider
func NewOllamaProvider(host, model string) *OllamaProvider {
	if host == "" {
		host = "http://localhost:11434"
	}

	client, _ := api.ClientFromEnvironment()
	if client == nil {
		client = api.NewClient(host, nil)
	}

	return &OllamaProvider{
		client: client,
		host:   host,
		model:  model,
	}
}

// Name returns the provider name
func (p *OllamaProvider) Name() string {
	return "ollama"
}

// Chat sends a message and returns the response
func (p *OllamaProvider) Chat(ctx context.Context, request *ChatRequest) (*ChatResponse, error) {
	if request.Model == "" {
		request.Model = p.model
	}

	// Convert messages to Ollama format
	messages := make([]api.Message, len(request.Messages))
	for i, msg := range request.Messages {
		messages[i] = api.Message{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	// Convert tools to Ollama format
	var tools []api.Tool
	if len(request.Tools) > 0 {
		tools = make([]api.Tool, len(request.Tools))
		for i, tool := range request.Tools {
			tools[i] = api.Tool{
				Type: tool.Type,
				Function: api.ToolFunction{
					Name:        tool.Function.Name,
					Description: tool.Function.Description,
					Parameters:  tool.Function.Parameters,
				},
			}
		}
	}

	chatRequest := &api.ChatRequest{
		Model:    request.Model,
		Messages: messages,
		Tools:    tools,
		Options: map[string]interface{}{
			"temperature": request.Temperature,
		},
	}

	if request.MaxTokens > 0 {
		chatRequest.Options["num_predict"] = request.MaxTokens
	}

	var response api.ChatResponse
	err := p.client.Chat(ctx, chatRequest, func(resp api.ChatResponse) error {
		response = resp
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("ollama chat request failed: %w", err)
	}

	// Convert response to standard format
	choices := []Choice{
		{
			Index: 0,
			Message: Message{
				Role:    response.Message.Role,
				Content: response.Message.Content,
			},
			FinishReason: "stop",
		},
	}

	// Handle tool calls if present
	if len(response.Message.ToolCalls) > 0 {
		toolCalls := make([]ToolCall, len(response.Message.ToolCalls))
		for i, tc := range response.Message.ToolCalls {
			args, _ := json.Marshal(tc.Function.Arguments)
			toolCalls[i] = ToolCall{
				ID:   fmt.Sprintf("call_%d", i),
				Type: "function",
				Function: ToolCallFunction{
					Name:      tc.Function.Name,
					Arguments: string(args),
				},
			}
		}
		choices[0].Message.ToolCalls = toolCalls
		choices[0].FinishReason = "tool_calls"
	}

	return &ChatResponse{
		ID:      fmt.Sprintf("chatcmpl-%d", time.Now().Unix()),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   request.Model,
		Choices: choices,
		Usage: Usage{
			PromptTokens:     response.PromptEvalCount,
			CompletionTokens: response.EvalCount,
			TotalTokens:      response.PromptEvalCount + response.EvalCount,
		},
	}, nil
}

// ChatStream sends a message and returns a streaming response
func (p *OllamaProvider) ChatStream(ctx context.Context, request *ChatRequest) (<-chan *ChatStreamResponse, error) {
	if request.Model == "" {
		request.Model = p.model
	}

	// Convert messages to Ollama format
	messages := make([]api.Message, len(request.Messages))
	for i, msg := range request.Messages {
		messages[i] = api.Message{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	// Convert tools to Ollama format
	var tools []api.Tool
	if len(request.Tools) > 0 {
		tools = make([]api.Tool, len(request.Tools))
		for i, tool := range request.Tools {
			tools[i] = api.Tool{
				Type: tool.Type,
				Function: api.ToolFunction{
					Name:        tool.Function.Name,
					Description: tool.Function.Description,
					Parameters:  tool.Function.Parameters,
				},
			}
		}
	}

	chatRequest := &api.ChatRequest{
		Model:    request.Model,
		Messages: messages,
		Tools:    tools,
		Stream:   &[]bool{true}[0],
		Options: map[string]interface{}{
			"temperature": request.Temperature,
		},
	}

	if request.MaxTokens > 0 {
		chatRequest.Options["num_predict"] = request.MaxTokens
	}

	responseChan := make(chan *ChatStreamResponse, 100)

	go func() {
		defer close(responseChan)

		err := p.client.Chat(ctx, chatRequest, func(resp api.ChatResponse) error {
			streamResp := &ChatStreamResponse{
				ID:      fmt.Sprintf("chatcmpl-%d", time.Now().Unix()),
				Object:  "chat.completion.chunk",
				Created: time.Now().Unix(),
				Model:   request.Model,
				Choices: []StreamChoice{
					{
						Index: 0,
						Delta: MessageDelta{
							Role:    resp.Message.Role,
							Content: resp.Message.Content,
						},
					},
				},
				Done: resp.Done,
			}

			// Handle tool calls in streaming
			if len(resp.Message.ToolCalls) > 0 {
				toolCalls := make([]ToolCall, len(resp.Message.ToolCalls))
				for i, tc := range resp.Message.ToolCalls {
					args, _ := json.Marshal(tc.Function.Arguments)
					toolCalls[i] = ToolCall{
						ID:   fmt.Sprintf("call_%d", i),
						Type: "function",
						Function: ToolCallFunction{
							Name:      tc.Function.Name,
							Arguments: string(args),
						},
					}
				}
				streamResp.Choices[0].Delta.ToolCalls = toolCalls
				streamResp.Choices[0].FinishReason = "tool_calls"
			}

			if resp.Done {
				streamResp.Choices[0].FinishReason = "stop"
			}

			responseChan <- streamResp
			return nil
		})

		if err != nil {
			responseChan <- &ChatStreamResponse{
				Error: fmt.Sprintf("ollama chat stream failed: %v", err),
				Done:  true,
			}
		}
	}()

	return responseChan, nil
}

// IsAvailable checks if the provider is available
func (p *OllamaProvider) IsAvailable(ctx context.Context) error {
	err := p.client.Heartbeat(ctx)
	if err != nil {
		return fmt.Errorf("ollama not available: %w", err)
	}
	return nil
}

// GetModels returns available models
func (p *OllamaProvider) GetModels(ctx context.Context) ([]Model, error) {
	listResp, err := p.client.List(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list models: %w", err)
	}

	models := make([]Model, len(listResp.Models))
	for i, model := range listResp.Models {
		models[i] = Model{
			ID:      model.Name,
			Object:  "model",
			Created: model.ModifiedAt.Unix(),
			OwnedBy: "ollama",
		}
	}

	return models, nil
}

// GetSystemPrompt returns the system prompt for Ollama
func (p *OllamaProvider) GetSystemPrompt() string {
	return `You are Arien AI, a powerful AI assistant with access to various tools for helping users accomplish tasks. You have the following capabilities:

CORE CAPABILITIES:
- Execute bash commands and shell operations
- Search for content in files using grep patterns
- Find files using glob patterns
- Create, edit, and manage files
- Search the web for real-time information
- Think step-by-step and use multiple tools in sequence or parallel

IMPORTANT GUIDELINES:
1. ALWAYS respond with tool outputs in JSON format only
2. NEVER show raw tool outputs in the CLI interface
3. Think carefully about which tools to use and when
4. Use multiple tools when necessary to complete complex tasks
5. Provide clear, helpful responses based on tool results
6. If a tool fails, try alternative approaches
7. Always validate your actions before proceeding

TOOL USAGE RULES:
- Use 'bash' for executing system commands and operations
- Use 'grep' for searching content within files
- Use 'glob' for finding files by name patterns
- Use 'write' for creating new files or updating existing ones
- Use 'edit' for making specific changes to files
- Use 'web' for getting real-time information from the internet

NEVER GIVE UP APPROACH:
- If a command fails, analyze the error and try alternatives
- Use retry logic with exponential backoff for temporary failures
- Break down complex tasks into smaller, manageable steps
- Always provide helpful feedback to the user about what you're doing

Remember: Your goal is to help users accomplish their tasks efficiently and effectively using the available tools.`
}
