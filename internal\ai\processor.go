package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/arien-ai/cli/internal/ai/providers"
	"github.com/arien-ai/cli/internal/ai/tools"
	"github.com/arien-ai/cli/internal/config"
)

// Processor handles AI request processing with tool calling
type Processor struct {
	provider     providers.Provider
	toolRegistry *tools.Registry
	retryManager *RetryManager
	config       *config.Config
}

// NewProcessor creates a new AI processor
func NewProcessor(provider providers.Provider, toolRegistry *tools.Registry, cfg *config.Config) *Processor {
	return &Processor{
		provider:     provider,
		toolRegistry: toolRegistry,
		retryManager: NewRetryManager(&cfg.Retry),
		config:       cfg,
	}
}

// ProcessRequest processes a user request with AI and tool calling
func (p *Processor) ProcessRequest(ctx context.Context, userMessage string) (*ProcessResult, error) {
	result := &ProcessResult{
		UserMessage:   userMessage,
		StartTime:     time.Now(),
		ToolCalls:     make([]ToolCallResult, 0),
		Conversations: make([]ConversationTurn, 0),
	}

	// Create initial conversation with system prompt and user message
	messages := []providers.Message{
		{
			Role:    "system",
			Content: p.getSystemPrompt(),
		},
		{
			Role:    "user",
			Content: userMessage,
		},
	}

	// Get available tools
	availableTools := p.getAvailableTools()

	// Process with retry logic
	err := p.retryManager.Execute(ctx, "ai_request_processing", func(ctx context.Context, attempt int) error {
		return p.processWithAI(ctx, messages, availableTools, result)
	})

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	if err != nil {
		result.Error = err.Error()
		return result, err
	}

	result.Success = true
	return result, nil
}

// ProcessRequestStream processes a request with streaming response
func (p *Processor) ProcessRequestStream(ctx context.Context, userMessage string, streamCallback func(*StreamChunk)) (*ProcessResult, error) {
	result := &ProcessResult{
		UserMessage:   userMessage,
		StartTime:     time.Now(),
		ToolCalls:     make([]ToolCallResult, 0),
		Conversations: make([]ConversationTurn, 0),
	}

	// Create initial conversation
	messages := []providers.Message{
		{
			Role:    "system",
			Content: p.getSystemPrompt(),
		},
		{
			Role:    "user",
			Content: userMessage,
		},
	}

	// Get available tools
	availableTools := p.getAvailableTools()

	// Process with streaming
	err := p.processWithAIStream(ctx, messages, availableTools, result, streamCallback)

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	if err != nil {
		result.Error = err.Error()
		return result, err
	}

	result.Success = true
	return result, nil
}

// processWithAI handles the core AI processing logic
func (p *Processor) processWithAI(ctx context.Context, messages []providers.Message, tools []providers.Tool, result *ProcessResult) error {
	maxIterations := 10 // Prevent infinite loops
	
	for iteration := 0; iteration < maxIterations; iteration++ {
		// Create chat request
		request := &providers.ChatRequest{
			Model:       "", // Will be set by provider
			Messages:    messages,
			Tools:       tools,
			Temperature: 0.7,
			MaxTokens:   4000,
		}

		// Send request to AI provider
		response, err := p.provider.Chat(ctx, request)
		if err != nil {
			return fmt.Errorf("AI provider request failed: %w", err)
		}

		if len(response.Choices) == 0 {
			return fmt.Errorf("no response choices received from AI provider")
		}

		choice := response.Choices[0]
		assistantMessage := choice.Message

		// Add conversation turn
		turn := ConversationTurn{
			Role:      assistantMessage.Role,
			Content:   assistantMessage.Content,
			ToolCalls: assistantMessage.ToolCalls,
			Timestamp: time.Now(),
		}
		result.Conversations = append(result.Conversations, turn)

		// Add assistant message to conversation
		messages = append(messages, assistantMessage)

		// Check if AI wants to call tools
		if len(assistantMessage.ToolCalls) == 0 {
			// No tool calls, we're done
			result.FinalResponse = assistantMessage.Content
			break
		}

		// Execute tool calls
		toolResults := make([]providers.ToolResult, 0, len(assistantMessage.ToolCalls))
		
		for _, toolCall := range assistantMessage.ToolCalls {
			toolResult, err := p.executeToolCall(ctx, toolCall)
			if err != nil {
				// Create error result
				toolResult = &providers.ToolResult{
					ToolCallID: toolCall.ID,
					Success:    false,
					Error:      err.Error(),
					Content:    fmt.Sprintf("Tool execution failed: %v", err),
				}
			}

			toolResults = append(toolResults, *toolResult)
			
			// Add to result tracking
			result.ToolCalls = append(result.ToolCalls, ToolCallResult{
				ToolCall: toolCall,
				Result:   *toolResult,
				Duration: time.Since(turn.Timestamp),
			})

			// Add tool result message to conversation
			// Note: Tool results are typically handled differently in different providers
			// For now, we'll add them as assistant messages with the tool result content
			messages = append(messages, providers.Message{
				Role:    "assistant",
				Content: fmt.Sprintf("Tool %s result: %s", toolCall.Function.Name, toolResult.Content),
			})
		}

		// Continue the conversation with tool results
	}

	return nil
}

// processWithAIStream handles streaming AI processing
func (p *Processor) processWithAIStream(ctx context.Context, messages []providers.Message, tools []providers.Tool, result *ProcessResult, streamCallback func(*StreamChunk)) error {
	maxIterations := 10
	
	for iteration := 0; iteration < maxIterations; iteration++ {
		request := &providers.ChatRequest{
			Model:       "",
			Messages:    messages,
			Tools:       tools,
			Temperature: 0.7,
			MaxTokens:   4000,
		}

		// Send streaming request
		streamChan, err := p.provider.ChatStream(ctx, request)
		if err != nil {
			return fmt.Errorf("AI provider streaming request failed: %w", err)
		}

		var assistantMessage providers.Message
		var currentContent strings.Builder

		// Process stream
		for streamResp := range streamChan {
			if streamResp.Error != "" {
				return fmt.Errorf("streaming error: %s", streamResp.Error)
			}

			if len(streamResp.Choices) > 0 {
				choice := streamResp.Choices[0]
				delta := choice.Delta

				// Handle content streaming
				if delta.Content != "" {
					currentContent.WriteString(delta.Content)
					
					// Send stream chunk to callback
					if streamCallback != nil {
						streamCallback(&StreamChunk{
							Type:    "content",
							Content: delta.Content,
						})
					}
				}

				// Handle tool calls
				if len(delta.ToolCalls) > 0 {
					assistantMessage.ToolCalls = append(assistantMessage.ToolCalls, delta.ToolCalls...)
				}

				// Check if stream is finished
				if choice.FinishReason == "stop" || choice.FinishReason == "tool_calls" {
					break
				}
			}
		}

		// Finalize assistant message
		assistantMessage.Role = "assistant"
		assistantMessage.Content = currentContent.String()

		// Add conversation turn
		turn := ConversationTurn{
			Role:      assistantMessage.Role,
			Content:   assistantMessage.Content,
			ToolCalls: assistantMessage.ToolCalls,
			Timestamp: time.Now(),
		}
		result.Conversations = append(result.Conversations, turn)
		messages = append(messages, assistantMessage)

		// Process tool calls if any
		if len(assistantMessage.ToolCalls) == 0 {
			result.FinalResponse = assistantMessage.Content
			break
		}

		// Execute tool calls
		for _, toolCall := range assistantMessage.ToolCalls {
			if streamCallback != nil {
				streamCallback(&StreamChunk{
					Type:     "tool_call",
					ToolCall: &toolCall,
				})
			}

			toolResult, err := p.executeToolCall(ctx, toolCall)
			if err != nil {
				toolResult = &providers.ToolResult{
					ToolCallID: toolCall.ID,
					Success:    false,
					Error:      err.Error(),
					Content:    fmt.Sprintf("Tool execution failed: %v", err),
				}
			}

			result.ToolCalls = append(result.ToolCalls, ToolCallResult{
				ToolCall: toolCall,
				Result:   *toolResult,
				Duration: time.Since(turn.Timestamp),
			})

			messages = append(messages, providers.Message{
				Role:    "assistant",
				Content: fmt.Sprintf("Tool %s result: %s", toolCall.Function.Name, toolResult.Content),
			})

			if streamCallback != nil {
				streamCallback(&StreamChunk{
					Type:       "tool_result",
					ToolResult: toolResult,
				})
			}
		}
	}

	return nil
}

// executeToolCall executes a single tool call
func (p *Processor) executeToolCall(ctx context.Context, toolCall providers.ToolCall) (*providers.ToolResult, error) {
	// Get tool from registry
	tool, exists := p.toolRegistry.Get(toolCall.Function.Name)
	if !exists {
		return nil, fmt.Errorf("tool '%s' not found", toolCall.Function.Name)
	}

	// Check if tool is enabled
	if !tool.IsEnabled() {
		return nil, fmt.Errorf("tool '%s' is disabled", toolCall.Function.Name)
	}

	// Parse tool arguments
	var args map[string]interface{}
	if err := json.Unmarshal([]byte(toolCall.Function.Arguments), &args); err != nil {
		return nil, fmt.Errorf("failed to parse tool arguments: %w", err)
	}

	// Execute tool with retry logic
	var result *providers.ToolResult
	err := p.retryManager.Execute(ctx, fmt.Sprintf("tool_%s", toolCall.Function.Name), func(ctx context.Context, attempt int) error {
		var err error
		result, err = tool.Execute(ctx, args)
		return err
	})

	if err != nil {
		return nil, err
	}

	// Ensure tool call ID is set
	result.ToolCallID = toolCall.ID
	return result, nil
}

// getSystemPrompt returns the system prompt for the AI
func (p *Processor) getSystemPrompt() string {
	return `You are Arien AI, an intelligent CLI assistant with access to powerful tools. Your role is to help users accomplish tasks by thinking step-by-step and using the available tools effectively.

CORE CAPABILITIES:
- Execute bash commands and shell operations
- Search for content in files using grep patterns
- Find files using glob patterns
- Create, edit, and manage files
- Search the web for real-time information
- Think step-by-step and use multiple tools in sequence or parallel

IMPORTANT GUIDELINES:
1. ALWAYS respond with tool outputs in JSON format only
2. NEVER show raw tool outputs in the CLI interface
3. Think carefully about which tools to use and when
4. Use multiple tools when necessary to complete complex tasks
5. Provide clear, helpful responses based on tool results
6. If a tool fails, try alternative approaches
7. Always validate your actions before proceeding

TOOL USAGE RULES:
- Use bash tool for command execution and system operations
- Use grep tool for searching content within files
- Use glob tool for finding files by name patterns
- Use write tool for creating new files or overwriting existing ones
- Use edit tool for modifying existing files
- Use web tool for searching the internet for current information

RESPONSE FORMAT:
- Provide thoughtful analysis of tool results
- Explain what you found and what it means
- Suggest next steps when appropriate
- Be concise but informative
- Focus on helping the user achieve their goals

Remember: You are a helpful assistant that never gives up. If one approach doesn't work, try another. Use your tools wisely to provide the best possible assistance.`
}

// getAvailableTools returns the list of available tools for the AI
func (p *Processor) getAvailableTools() []providers.Tool {
	tools := make([]providers.Tool, 0)

	for _, tool := range p.toolRegistry.GetAll() {
		if tool.IsEnabled() {
			tools = append(tools, providers.Tool{
				Type: "function",
				Function: providers.ToolFunction{
					Name:        tool.Name(),
					Description: tool.Description(),
					Parameters:  tool.Parameters(),
				},
			})
		}
	}

	return tools
}

// ProcessResult represents the result of processing a user request
type ProcessResult struct {
	UserMessage   string              `json:"user_message"`
	FinalResponse string              `json:"final_response"`
	Success       bool                `json:"success"`
	Error         string              `json:"error,omitempty"`
	StartTime     time.Time           `json:"start_time"`
	EndTime       time.Time           `json:"end_time"`
	Duration      time.Duration       `json:"duration"`
	ToolCalls     []ToolCallResult    `json:"tool_calls"`
	Conversations []ConversationTurn  `json:"conversations"`
}

// ToolCallResult represents the result of a tool call
type ToolCallResult struct {
	ToolCall providers.ToolCall   `json:"tool_call"`
	Result   providers.ToolResult `json:"result"`
	Duration time.Duration        `json:"duration"`
}

// ConversationTurn represents a turn in the conversation
type ConversationTurn struct {
	Role      string                `json:"role"`
	Content   string                `json:"content"`
	ToolCalls []providers.ToolCall  `json:"tool_calls,omitempty"`
	Timestamp time.Time             `json:"timestamp"`
}

// StreamChunk represents a chunk of streaming data
type StreamChunk struct {
	Type       string                 `json:"type"` // "content", "tool_call", "tool_result"
	Content    string                 `json:"content,omitempty"`
	ToolCall   *providers.ToolCall    `json:"tool_call,omitempty"`
	ToolResult *providers.ToolResult  `json:"tool_result,omitempty"`
}

// GetSummary returns a summary of the processing result
func (pr *ProcessResult) GetSummary() string {
	if !pr.Success {
		return fmt.Sprintf("Request failed: %s", pr.Error)
	}

	summary := fmt.Sprintf("Request completed in %v", pr.Duration)
	if len(pr.ToolCalls) > 0 {
		summary += fmt.Sprintf(" with %d tool calls", len(pr.ToolCalls))
	}

	return summary
}

// GetToolCallsSummary returns a summary of tool calls
func (pr *ProcessResult) GetToolCallsSummary() []string {
	summaries := make([]string, len(pr.ToolCalls))

	for i, tc := range pr.ToolCalls {
		status := "✓"
		if !tc.Result.Success {
			status = "✗"
		}

		summaries[i] = fmt.Sprintf("%s %s (%v)",
			status, tc.ToolCall.Function.Name, tc.Duration)
	}

	return summaries
}
