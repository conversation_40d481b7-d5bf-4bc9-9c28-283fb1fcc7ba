package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	AI       AIConfig       `mapstructure:"ai"`
	CLI      CLIConfig      `mapstructure:"cli"`
	Tools    ToolsConfig    `mapstructure:"tools"`
	Retry    RetryConfig    `mapstructure:"retry"`
	Logging  LoggingConfig  `mapstructure:"logging"`
}

// AIConfig contains AI provider configurations
type AIConfig struct {
	Provider string            `mapstructure:"provider"`
	Deepseek DeepseekConfig    `mapstructure:"deepseek"`
	Ollama   OllamaConfig      `mapstructure:"ollama"`
	Models   map[string]string `mapstructure:"models"`
}

// DeepseekConfig contains Deepseek API configuration
type DeepseekConfig struct {
	APIKey  string `mapstructure:"api_key"`
	BaseURL string `mapstructure:"base_url"`
	Model   string `mapstructure:"model"`
}

// OllamaConfig contains Ollama configuration
type OllamaConfig struct {
	Host  string `mapstructure:"host"`
	Model string `mapstructure:"model"`
}

// CLIConfig contains CLI interface configuration
type CLIConfig struct {
	Theme         string `mapstructure:"theme"`
	Animation     bool   `mapstructure:"animation"`
	ShowProgress  bool   `mapstructure:"show_progress"`
	AutoApprove   bool   `mapstructure:"auto_approve"`
	MaxHistory    int    `mapstructure:"max_history"`
}

// ToolsConfig contains tool-specific configurations
type ToolsConfig struct {
	Bash ToolConfig `mapstructure:"bash"`
	Grep ToolConfig `mapstructure:"grep"`
	Glob ToolConfig `mapstructure:"glob"`
	Web  ToolConfig `mapstructure:"web"`
}

// ToolConfig represents individual tool configuration
type ToolConfig struct {
	Enabled bool                   `mapstructure:"enabled"`
	Options map[string]interface{} `mapstructure:"options"`
}

// RetryConfig contains retry logic configuration
type RetryConfig struct {
	MaxAttempts     int     `mapstructure:"max_attempts"`
	InitialDelay    int     `mapstructure:"initial_delay_ms"`
	MaxDelay        int     `mapstructure:"max_delay_ms"`
	BackoffFactor   float64 `mapstructure:"backoff_factor"`
	EnableRetry     bool    `mapstructure:"enable_retry"`
}

// LoggingConfig contains logging configuration
type LoggingConfig struct {
	Level    string `mapstructure:"level"`
	Format   string `mapstructure:"format"`
	File     string `mapstructure:"file"`
	Console  bool   `mapstructure:"console"`
}

// DefaultConfig returns a configuration with sensible defaults
func DefaultConfig() *Config {
	return &Config{
		AI: AIConfig{
			Provider: "deepseek",
			Deepseek: DeepseekConfig{
				BaseURL: "https://api.deepseek.com",
				Model:   "deepseek-chat",
			},
			Ollama: OllamaConfig{
				Host:  "http://localhost:11434",
				Model: "llama3.2",
			},
			Models: map[string]string{
				"deepseek-chat":     "deepseek-chat",
				"deepseek-reasoner": "deepseek-reasoner",
			},
		},
		CLI: CLIConfig{
			Theme:        "default",
			Animation:    true,
			ShowProgress: true,
			AutoApprove:  false,
			MaxHistory:   100,
		},
		Tools: ToolsConfig{
			Bash: ToolConfig{Enabled: true, Options: map[string]interface{}{}},
			Grep: ToolConfig{Enabled: true, Options: map[string]interface{}{}},
			Glob: ToolConfig{Enabled: true, Options: map[string]interface{}{}},
			Web:  ToolConfig{Enabled: true, Options: map[string]interface{}{}},
		},
		Retry: RetryConfig{
			MaxAttempts:   3,
			InitialDelay:  1000,
			MaxDelay:      30000,
			BackoffFactor: 2.0,
			EnableRetry:   true,
		},
		Logging: LoggingConfig{
			Level:   "info",
			Format:  "text",
			Console: true,
		},
	}
}

// Load loads configuration from file and environment variables
func Load() (*Config, error) {
	config := DefaultConfig()
	
	// Set up viper
	viper.SetConfigName("arien-ai")
	viper.SetConfigType("yaml")
	
	// Add config paths
	home, err := os.UserHomeDir()
	if err == nil {
		viper.AddConfigPath(filepath.Join(home, ".config", "arien-ai"))
		viper.AddConfigPath(filepath.Join(home, ".arien-ai"))
	}
	viper.AddConfigPath(".")
	
	// Environment variable support
	viper.SetEnvPrefix("ARIEN_AI")
	viper.AutomaticEnv()
	
	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("error reading config file: %w", err)
		}
	}
	
	// Unmarshal into config struct
	if err := viper.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("error unmarshaling config: %w", err)
	}
	
	return config, nil
}

// Save saves the configuration to file
func (c *Config) Save() error {
	home, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("error getting home directory: %w", err)
	}
	
	configDir := filepath.Join(home, ".config", "arien-ai")
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("error creating config directory: %w", err)
	}
	
	configFile := filepath.Join(configDir, "arien-ai.yaml")
	
	viper.SetConfigFile(configFile)
	viper.Set("ai", c.AI)
	viper.Set("cli", c.CLI)
	viper.Set("tools", c.Tools)
	viper.Set("retry", c.Retry)
	viper.Set("logging", c.Logging)
	
	return viper.WriteConfig()
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.AI.Provider == "" {
		return fmt.Errorf("AI provider must be specified")
	}
	
	switch c.AI.Provider {
	case "deepseek":
		if c.AI.Deepseek.APIKey == "" {
			return fmt.Errorf("Deepseek API key is required")
		}
	case "ollama":
		if c.AI.Ollama.Host == "" {
			return fmt.Errorf("Ollama host is required")
		}
	default:
		return fmt.Errorf("unsupported AI provider: %s", c.AI.Provider)
	}
	
	return nil
}
