# Arien AI CLI Configuration Example
# Copy this file to ~/.config/arien-ai/arien-ai.yaml and customize

# AI Provider Configuration
ai:
  # Current provider: deepseek or ollama
  provider: deepseek
  
  # Deepseek Configuration
  deepseek:
    # Get your API key from https://platform.deepseek.com/
    api_key: "your-deepseek-api-key-here"
    base_url: "https://api.deepseek.com"
    model: "deepseek-chat"  # or "deepseek-reasoner"
  
  # Ollama Configuration (for local AI)
  ollama:
    host: "http://localhost:11434"
    model: "llama3.2"  # or any other model you have installed
  
  # Model mappings for different use cases
  models:
    deepseek-chat: "deepseek-chat"
    deepseek-reasoner: "deepseek-reasoner"
    llama: "llama3.2"
    codellama: "codellama:latest"

# CLI Interface Configuration
cli:
  # UI theme: default, dark, light, minimal
  theme: default
  
  # Enable animations and progress indicators
  animation: true
  show_progress: true
  
  # Auto-approve tool executions (use with caution)
  auto_approve: false
  
  # Maximum number of commands to keep in history
  max_history: 100

# Tool Configuration
tools:
  # Bash command execution
  bash:
    enabled: true
    options:
      default_timeout: 30  # seconds
      safe_mode: true      # prevent dangerous commands
  
  # Content search tool
  grep:
    enabled: true
    options:
      max_results: 50
      default_context_lines: 2
  
  # File pattern matching
  glob:
    enabled: true
    options:
      max_results: 100
      include_hidden: false
  
  # File writing tool
  write:
    enabled: true
    options:
      default_permissions: "0644"
      backup_by_default: true
  
  # File editing tool
  edit:
    enabled: true
    options:
      backup_by_default: true
      max_file_size: "10MB"
  
  # Web search tool
  web:
    enabled: true
    options:
      max_results: 5
      safe_search: true
      default_language: "en"
      default_region: "us"

# Retry Logic Configuration
retry:
  # Enable automatic retry for failed operations
  enable_retry: true
  
  # Maximum number of retry attempts
  max_attempts: 3
  
  # Initial delay between retries (milliseconds)
  initial_delay_ms: 1000
  
  # Maximum delay between retries (milliseconds)
  max_delay_ms: 30000
  
  # Exponential backoff factor
  backoff_factor: 2.0

# Logging Configuration
logging:
  # Log level: debug, info, warn, error
  level: info
  
  # Log format: text, json
  format: text
  
  # Log to file (optional)
  # file: "/var/log/arien-ai/arien.log"
  
  # Also log to console
  console: true

# Advanced Configuration
advanced:
  # Circuit breaker settings
  circuit_breaker:
    enabled: true
    max_failures: 5
    reset_timeout: "5m"
  
  # Rate limiting
  rate_limit:
    enabled: false
    requests_per_minute: 60
  
  # Caching
  cache:
    enabled: true
    ttl: "1h"
    max_size: "100MB"

# Environment-specific overrides
environments:
  development:
    logging:
      level: debug
    cli:
      animation: true
    retry:
      max_attempts: 1
  
  production:
    logging:
      level: warn
      file: "/var/log/arien-ai/arien.log"
    cli:
      auto_approve: false
    retry:
      max_attempts: 5

# Security Configuration
security:
  # Allowed commands for bash tool (empty = all allowed)
  allowed_commands: []
  
  # Blocked commands for bash tool
  blocked_commands:
    - "rm -rf /"
    - ":(){ :|:& };:"  # fork bomb
    - "dd if=/dev/zero"
    - "chmod -R 777 /"
  
  # Allowed file patterns for write/edit tools
  allowed_file_patterns:
    - "*.txt"
    - "*.md"
    - "*.yaml"
    - "*.yml"
    - "*.json"
    - "*.go"
    - "*.js"
    - "*.ts"
    - "*.py"
    - "*.sh"
  
  # Blocked file patterns
  blocked_file_patterns:
    - "/etc/passwd"
    - "/etc/shadow"
    - "*.key"
    - "*.pem"
    - "id_rsa*"

# Integration Configuration
integrations:
  # GitHub integration
  github:
    enabled: false
    token: ""
    default_org: ""
  
  # Docker integration
  docker:
    enabled: true
    default_registry: "docker.io"
  
  # Kubernetes integration
  kubernetes:
    enabled: false
    default_context: ""
    default_namespace: "default"

# Notification Configuration
notifications:
  # Desktop notifications
  desktop:
    enabled: true
    on_completion: true
    on_error: true
  
  # Webhook notifications
  webhook:
    enabled: false
    url: ""
    events: ["completion", "error"]

# Plugin Configuration
plugins:
  # Plugin directory
  directory: "~/.config/arien-ai/plugins"
  
  # Auto-load plugins
  auto_load: true
  
  # Enabled plugins
  enabled: []

# Experimental Features
experimental:
  # Enable experimental features
  enabled: false
  
  # Feature flags
  features:
    multi_provider: false
    voice_input: false
    gui_mode: false
    collaborative_mode: false
