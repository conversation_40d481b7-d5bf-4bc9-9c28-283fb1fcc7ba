package utils

import (
	"fmt"
	"runtime"
	"strings"
)

// ErrorType represents different types of errors
type ErrorType int

const (
	ErrorTypeUnknown ErrorType = iota
	ErrorTypeValidation
	ErrorTypeNetwork
	ErrorTypeFileSystem
	ErrorTypePermission
	ErrorTypeConfiguration
	ErrorTypeProvider
	ErrorTypeTool
	ErrorTypeRetryable
)

// String returns the string representation of ErrorType
func (et ErrorType) String() string {
	switch et {
	case ErrorTypeValidation:
		return "validation"
	case ErrorTypeNetwork:
		return "network"
	case ErrorTypeFileSystem:
		return "filesystem"
	case ErrorTypePermission:
		return "permission"
	case ErrorTypeConfiguration:
		return "configuration"
	case ErrorTypeProvider:
		return "provider"
	case ErrorTypeTool:
		return "tool"
	case ErrorTypeRetryable:
		return "retryable"
	default:
		return "unknown"
	}
}

// ArienError represents a structured error with additional context
type ArienError struct {
	Type        ErrorType
	Message     string
	Cause       error
	Context     map[string]interface{}
	StackTrace  []string
	Retryable   bool
	UserMessage string
}

// Error implements the error interface
func (e *ArienError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Type.String(), e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Type.String(), e.Message)
}

// Unwrap returns the underlying error
func (e *ArienError) Unwrap() error {
	return e.Cause
}

// IsRetryable returns whether the error is retryable
func (e *ArienError) IsRetryable() bool {
	return e.Retryable
}

// GetUserMessage returns a user-friendly error message
func (e *ArienError) GetUserMessage() string {
	if e.UserMessage != "" {
		return e.UserMessage
	}
	return e.Message
}

// AddContext adds context information to the error
func (e *ArienError) AddContext(key string, value interface{}) *ArienError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// GetContext retrieves context information
func (e *ArienError) GetContext(key string) (interface{}, bool) {
	if e.Context == nil {
		return nil, false
	}
	value, exists := e.Context[key]
	return value, exists
}

// NewError creates a new ArienError
func NewError(errorType ErrorType, message string) *ArienError {
	return &ArienError{
		Type:       errorType,
		Message:    message,
		StackTrace: captureStackTrace(),
		Context:    make(map[string]interface{}),
	}
}

// NewErrorWithCause creates a new ArienError with a cause
func NewErrorWithCause(errorType ErrorType, message string, cause error) *ArienError {
	return &ArienError{
		Type:       errorType,
		Message:    message,
		Cause:      cause,
		StackTrace: captureStackTrace(),
		Context:    make(map[string]interface{}),
	}
}

// NewRetryableError creates a new retryable error
func NewRetryableError(errorType ErrorType, message string, cause error) *ArienError {
	return &ArienError{
		Type:       errorType,
		Message:    message,
		Cause:      cause,
		Retryable:  true,
		StackTrace: captureStackTrace(),
		Context:    make(map[string]interface{}),
	}
}

// WrapError wraps an existing error with additional context
func WrapError(err error, errorType ErrorType, message string) *ArienError {
	if arienErr, ok := err.(*ArienError); ok {
		// If it's already an ArienError, just update the message
		arienErr.Message = message + ": " + arienErr.Message
		return arienErr
	}
	
	return &ArienError{
		Type:       errorType,
		Message:    message,
		Cause:      err,
		StackTrace: captureStackTrace(),
		Context:    make(map[string]interface{}),
	}
}

// captureStackTrace captures the current stack trace
func captureStackTrace() []string {
	var stackTrace []string
	
	// Skip the first few frames (this function and error creation)
	for i := 2; i < 10; i++ {
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}
		
		fn := runtime.FuncForPC(pc)
		if fn == nil {
			continue
		}
		
		// Format: function (file:line)
		frame := fmt.Sprintf("%s (%s:%d)", fn.Name(), file, line)
		stackTrace = append(stackTrace, frame)
	}
	
	return stackTrace
}

// IsErrorType checks if an error is of a specific type
func IsErrorType(err error, errorType ErrorType) bool {
	if arienErr, ok := err.(*ArienError); ok {
		return arienErr.Type == errorType
	}
	return false
}

// IsRetryableError checks if an error is retryable
func IsRetryableError(err error) bool {
	if arienErr, ok := err.(*ArienError); ok {
		return arienErr.Retryable
	}
	
	// Check for common retryable error patterns
	errStr := strings.ToLower(err.Error())
	retryablePatterns := []string{
		"timeout",
		"connection refused",
		"connection reset",
		"network is unreachable",
		"temporary failure",
		"rate limit",
		"too many requests",
		"429",
		"500",
		"502",
		"503",
		"504",
	}
	
	for _, pattern := range retryablePatterns {
		if strings.Contains(errStr, pattern) {
			return true
		}
	}
	
	return false
}

// GetErrorChain returns the chain of errors
func GetErrorChain(err error) []error {
	var chain []error
	
	for err != nil {
		chain = append(chain, err)
		
		// Try to unwrap the error
		if unwrapper, ok := err.(interface{ Unwrap() error }); ok {
			err = unwrapper.Unwrap()
		} else {
			break
		}
	}
	
	return chain
}

// FormatError formats an error for display
func FormatError(err error) string {
	if arienErr, ok := err.(*ArienError); ok {
		var parts []string
		
		// Add error type and message
		parts = append(parts, fmt.Sprintf("[%s] %s", 
			strings.ToUpper(arienErr.Type.String()), 
			arienErr.GetUserMessage()))
		
		// Add context if available
		if len(arienErr.Context) > 0 {
			var contextParts []string
			for key, value := range arienErr.Context {
				contextParts = append(contextParts, fmt.Sprintf("%s=%v", key, value))
			}
			parts = append(parts, fmt.Sprintf("Context: %s", strings.Join(contextParts, ", ")))
		}
		
		// Add cause if available
		if arienErr.Cause != nil {
			parts = append(parts, fmt.Sprintf("Caused by: %v", arienErr.Cause))
		}
		
		return strings.Join(parts, "\n")
	}
	
	return err.Error()
}

// Common error constructors

// ValidationError creates a validation error
func ValidationError(message string) *ArienError {
	return NewError(ErrorTypeValidation, message).
		AddContext("user_friendly", true)
}

// NetworkError creates a network error
func NetworkError(message string, cause error) *ArienError {
	return NewRetryableError(ErrorTypeNetwork, message, cause).
		AddContext("user_friendly", true)
}

// FileSystemError creates a filesystem error
func FileSystemError(message string, cause error) *ArienError {
	return NewErrorWithCause(ErrorTypeFileSystem, message, cause).
		AddContext("user_friendly", true)
}

// PermissionError creates a permission error
func PermissionError(message string) *ArienError {
	return NewError(ErrorTypePermission, message).
		AddContext("user_friendly", true)
}

// ConfigurationError creates a configuration error
func ConfigurationError(message string) *ArienError {
	return NewError(ErrorTypeConfiguration, message).
		AddContext("user_friendly", true)
}

// ProviderError creates a provider error
func ProviderError(message string, cause error) *ArienError {
	return NewRetryableError(ErrorTypeProvider, message, cause).
		AddContext("user_friendly", true)
}

// ToolError creates a tool error
func ToolError(toolName, message string, cause error) *ArienError {
	return NewErrorWithCause(ErrorTypeTool, message, cause).
		AddContext("tool", toolName).
		AddContext("user_friendly", true)
}

// ErrorRecovery provides suggestions for error recovery
func ErrorRecovery(err error) []string {
	var suggestions []string
	
	if arienErr, ok := err.(*ArienError); ok {
		switch arienErr.Type {
		case ErrorTypeNetwork:
			suggestions = append(suggestions, 
				"Check your internet connection",
				"Verify the service URL is correct",
				"Try again in a few moments")
		case ErrorTypePermission:
			suggestions = append(suggestions,
				"Check file/directory permissions",
				"Run with appropriate privileges",
				"Verify you have access to the resource")
		case ErrorTypeConfiguration:
			suggestions = append(suggestions,
				"Check your configuration file",
				"Verify API keys and credentials",
				"Run 'arien config show' to review settings")
		case ErrorTypeProvider:
			suggestions = append(suggestions,
				"Check your AI provider configuration",
				"Verify API keys are valid",
				"Try switching to a different provider")
		case ErrorTypeTool:
			if toolName, exists := arienErr.GetContext("tool"); exists {
				suggestions = append(suggestions,
					fmt.Sprintf("Check if the '%s' tool is properly configured", toolName),
					"Try disabling and re-enabling the tool",
					"Verify tool dependencies are installed")
			}
		}
		
		if arienErr.Retryable {
			suggestions = append(suggestions, "This error is temporary and will be retried automatically")
		}
	}
	
	if len(suggestions) == 0 {
		suggestions = append(suggestions, "Please check the error details and try again")
	}
	
	return suggestions
}
