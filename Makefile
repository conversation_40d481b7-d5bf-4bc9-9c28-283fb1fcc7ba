# Arien AI CLI Makefile
# Supports Go 1.24.0 and cross-platform builds

# Variables
BINARY_NAME := arien
PACKAGE := github.com/arien-ai/cli
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
DATE := $(shell date -u +"%Y-%m-%dT%H:%M:%SZ")
LDFLAGS := -ldflags "-X main.version=$(VERSION) -X main.commit=$(COMMIT) -X main.date=$(DATE)"

# Go settings
GO := go
GOOS := $(shell go env GOOS)
GOARCH := $(shell go env GOARCH)
GOVERSION := $(shell go version | cut -d' ' -f3)

# Directories
BUILD_DIR := build
DIST_DIR := dist
SCRIPTS_DIR := scripts
CMD_DIR := cmd/arien

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
CYAN := \033[0;36m
NC := \033[0m # No Color

# Default target
.PHONY: all
all: clean test build

# Help target
.PHONY: help
help:
	@echo "$(CYAN)Arien AI CLI Build System$(NC)"
	@echo ""
	@echo "$(YELLOW)Available targets:$(NC)"
	@echo "  $(GREEN)build$(NC)          Build binary for current platform"
	@echo "  $(GREEN)build-all$(NC)      Build binaries for all platforms"
	@echo "  $(GREEN)test$(NC)           Run tests"
	@echo "  $(GREEN)test-verbose$(NC)   Run tests with verbose output"
	@echo "  $(GREEN)test-coverage$(NC)  Run tests with coverage report"
	@echo "  $(GREEN)lint$(NC)           Run linters"
	@echo "  $(GREEN)fmt$(NC)            Format code"
	@echo "  $(GREEN)clean$(NC)          Clean build artifacts"
	@echo "  $(GREEN)deps$(NC)           Download dependencies"
	@echo "  $(GREEN)deps-update$(NC)    Update dependencies"
	@echo "  $(GREEN)install$(NC)        Install binary locally"
	@echo "  $(GREEN)uninstall$(NC)      Uninstall binary"
	@echo "  $(GREEN)release$(NC)        Create release packages"
	@echo "  $(GREEN)docker$(NC)         Build Docker image"
	@echo "  $(GREEN)run$(NC)            Run the application"
	@echo "  $(GREEN)dev$(NC)            Run in development mode"
	@echo ""
	@echo "$(YELLOW)Variables:$(NC)"
	@echo "  VERSION=$(VERSION)"
	@echo "  COMMIT=$(COMMIT)"
	@echo "  DATE=$(DATE)"
	@echo "  GOOS=$(GOOS)"
	@echo "  GOARCH=$(GOARCH)"
	@echo "  GO_VERSION=$(GOVERSION)"

# Build targets
.PHONY: build
build:
	@echo "$(BLUE)Building $(BINARY_NAME) for $(GOOS)/$(GOARCH)...$(NC)"
	@mkdir -p $(BUILD_DIR)
	$(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(CMD_DIR)/main.go
	@echo "$(GREEN)Build complete: $(BUILD_DIR)/$(BINARY_NAME)$(NC)"

.PHONY: build-all
build-all: clean
	@echo "$(BLUE)Building $(BINARY_NAME) for all platforms...$(NC)"
	@mkdir -p $(DIST_DIR)
	
	# Linux builds
	@echo "$(YELLOW)Building for Linux...$(NC)"
	GOOS=linux GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME)_linux_amd64 $(CMD_DIR)/main.go
	GOOS=linux GOARCH=arm64 $(GO) build $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME)_linux_arm64 $(CMD_DIR)/main.go
	GOOS=linux GOARCH=arm $(GO) build $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME)_linux_arm $(CMD_DIR)/main.go
	
	# macOS builds
	@echo "$(YELLOW)Building for macOS...$(NC)"
	GOOS=darwin GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME)_darwin_amd64 $(CMD_DIR)/main.go
	GOOS=darwin GOARCH=arm64 $(GO) build $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME)_darwin_arm64 $(CMD_DIR)/main.go
	
	# Windows builds
	@echo "$(YELLOW)Building for Windows...$(NC)"
	GOOS=windows GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME)_windows_amd64.exe $(CMD_DIR)/main.go
	GOOS=windows GOARCH=arm64 $(GO) build $(LDFLAGS) -o $(DIST_DIR)/$(BINARY_NAME)_windows_arm64.exe $(CMD_DIR)/main.go
	
	@echo "$(GREEN)All builds complete!$(NC)"
	@ls -la $(DIST_DIR)/

# Test targets
.PHONY: test
test:
	@echo "$(BLUE)Running tests...$(NC)"
	$(GO) test -race -short ./...
	@echo "$(GREEN)Tests passed!$(NC)"

.PHONY: test-verbose
test-verbose:
	@echo "$(BLUE)Running tests with verbose output...$(NC)"
	$(GO) test -race -short -v ./...

.PHONY: test-coverage
test-coverage:
	@echo "$(BLUE)Running tests with coverage...$(NC)"
	@mkdir -p $(BUILD_DIR)
	$(GO) test -race -coverprofile=$(BUILD_DIR)/coverage.out ./...
	$(GO) tool cover -html=$(BUILD_DIR)/coverage.out -o $(BUILD_DIR)/coverage.html
	@echo "$(GREEN)Coverage report generated: $(BUILD_DIR)/coverage.html$(NC)"

.PHONY: test-integration
test-integration:
	@echo "$(BLUE)Running integration tests...$(NC)"
	$(GO) test -tags=integration ./...

# Code quality targets
.PHONY: lint
lint:
	@echo "$(BLUE)Running linters...$(NC)"
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "$(YELLOW)golangci-lint not found, running basic checks...$(NC)"; \
		$(GO) vet ./...; \
		$(GO) fmt ./...; \
	fi
	@echo "$(GREEN)Linting complete!$(NC)"

.PHONY: fmt
fmt:
	@echo "$(BLUE)Formatting code...$(NC)"
	$(GO) fmt ./...
	@if command -v goimports >/dev/null 2>&1; then \
		goimports -w .; \
	fi
	@echo "$(GREEN)Code formatted!$(NC)"

.PHONY: vet
vet:
	@echo "$(BLUE)Running go vet...$(NC)"
	$(GO) vet ./...

# Dependency management
.PHONY: deps
deps:
	@echo "$(BLUE)Downloading dependencies...$(NC)"
	$(GO) mod download
	$(GO) mod verify
	@echo "$(GREEN)Dependencies downloaded!$(NC)"

.PHONY: deps-update
deps-update:
	@echo "$(BLUE)Updating dependencies...$(NC)"
	$(GO) get -u ./...
	$(GO) mod tidy
	@echo "$(GREEN)Dependencies updated!$(NC)"

.PHONY: deps-vendor
deps-vendor:
	@echo "$(BLUE)Vendoring dependencies...$(NC)"
	$(GO) mod vendor
	@echo "$(GREEN)Dependencies vendored!$(NC)"

# Installation targets
.PHONY: install
install: build
	@echo "$(BLUE)Installing $(BINARY_NAME)...$(NC)"
	@if [ -w /usr/local/bin ]; then \
		cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/; \
	else \
		sudo cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/; \
	fi
	@echo "$(GREEN)$(BINARY_NAME) installed to /usr/local/bin/$(NC)"

.PHONY: uninstall
uninstall:
	@echo "$(BLUE)Uninstalling $(BINARY_NAME)...$(NC)"
	@if [ -w /usr/local/bin ]; then \
		rm -f /usr/local/bin/$(BINARY_NAME); \
	else \
		sudo rm -f /usr/local/bin/$(BINARY_NAME); \
	fi
	@echo "$(GREEN)$(BINARY_NAME) uninstalled$(NC)"

# Release targets
.PHONY: release
release: clean build-all
	@echo "$(BLUE)Creating release packages...$(NC)"
	@mkdir -p $(DIST_DIR)/packages
	
	# Create tar.gz packages for Unix systems
	@for binary in $(DIST_DIR)/$(BINARY_NAME)_linux_* $(DIST_DIR)/$(BINARY_NAME)_darwin_*; do \
		if [ -f "$$binary" ]; then \
			base=$$(basename $$binary); \
			echo "$(YELLOW)Packaging $$base...$(NC)"; \
			tar -czf $(DIST_DIR)/packages/$$base.tar.gz -C $(DIST_DIR) $$base; \
		fi \
	done
	
	# Create zip packages for Windows
	@for binary in $(DIST_DIR)/$(BINARY_NAME)_windows_*; do \
		if [ -f "$$binary" ]; then \
			base=$$(basename $$binary .exe); \
			echo "$(YELLOW)Packaging $$base...$(NC)"; \
			cd $(DIST_DIR) && zip packages/$$base.zip $$(basename $$binary); \
		fi \
	done
	
	@echo "$(GREEN)Release packages created in $(DIST_DIR)/packages/$(NC)"

# Docker targets
.PHONY: docker
docker:
	@echo "$(BLUE)Building Docker image...$(NC)"
	docker build -t $(BINARY_NAME):$(VERSION) .
	docker tag $(BINARY_NAME):$(VERSION) $(BINARY_NAME):latest
	@echo "$(GREEN)Docker image built: $(BINARY_NAME):$(VERSION)$(NC)"

.PHONY: docker-run
docker-run:
	@echo "$(BLUE)Running Docker container...$(NC)"
	docker run -it --rm $(BINARY_NAME):latest

# Development targets
.PHONY: run
run: build
	@echo "$(BLUE)Running $(BINARY_NAME)...$(NC)"
	./$(BUILD_DIR)/$(BINARY_NAME)

.PHONY: dev
dev:
	@echo "$(BLUE)Running in development mode...$(NC)"
	$(GO) run $(CMD_DIR)/main.go

.PHONY: debug
debug:
	@echo "$(BLUE)Running with debug flags...$(NC)"
	$(GO) run -race $(CMD_DIR)/main.go --verbose

# Utility targets
.PHONY: clean
clean:
	@echo "$(BLUE)Cleaning build artifacts...$(NC)"
	rm -rf $(BUILD_DIR) $(DIST_DIR)
	$(GO) clean -cache -testcache -modcache
	@echo "$(GREEN)Clean complete!$(NC)"

.PHONY: version
version:
	@echo "Version: $(VERSION)"
	@echo "Commit: $(COMMIT)"
	@echo "Date: $(DATE)"
	@echo "Go Version: $(GOVERSION)"

.PHONY: info
info:
	@echo "$(CYAN)Project Information:$(NC)"
	@echo "  Name: $(BINARY_NAME)"
	@echo "  Package: $(PACKAGE)"
	@echo "  Version: $(VERSION)"
	@echo "  Commit: $(COMMIT)"
	@echo "  Build Date: $(DATE)"
	@echo "  Go Version: $(GOVERSION)"
	@echo "  Target OS: $(GOOS)"
	@echo "  Target Arch: $(GOARCH)"

# Check if Go 1.24 is being used
.PHONY: check-go-version
check-go-version:
	@echo "$(BLUE)Checking Go version...$(NC)"
	@if echo "$(GOVERSION)" | grep -q "go1.24"; then \
		echo "$(GREEN)✓ Using Go 1.24.x$(NC)"; \
	else \
		echo "$(YELLOW)⚠ Warning: This project is designed for Go 1.24.x, you are using $(GOVERSION)$(NC)"; \
	fi

# Setup development environment
.PHONY: setup-dev
setup-dev: check-go-version deps
	@echo "$(BLUE)Setting up development environment...$(NC)"
	@if ! command -v golangci-lint >/dev/null 2>&1; then \
		echo "$(YELLOW)Installing golangci-lint...$(NC)"; \
		curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $$(go env GOPATH)/bin; \
	fi
	@if ! command -v goimports >/dev/null 2>&1; then \
		echo "$(YELLOW)Installing goimports...$(NC)"; \
		$(GO) install golang.org/x/tools/cmd/goimports@latest; \
	fi
	@echo "$(GREEN)Development environment ready!$(NC)"

# CI/CD targets
.PHONY: ci
ci: check-go-version deps lint test build

.PHONY: ci-release
ci-release: ci release

# Make build directory if it doesn't exist
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)

$(DIST_DIR):
	@mkdir -p $(DIST_DIR)
