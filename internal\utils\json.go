package utils

import (
	"encoding/json"
	"fmt"
	"strings"
)

// PrettyPrintJSON formats JSON with proper indentation
func PrettyPrintJSON(data interface{}) (string, error) {
	jsonBytes, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal JSON: %w", err)
	}
	return string(jsonBytes), nil
}

// CompactJSON removes whitespace from JSON
func CompactJSON(data interface{}) (string, error) {
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("failed to marshal JSON: %w", err)
	}
	return string(jsonBytes), nil
}

// IsValidJSON checks if a string is valid JSON
func IsValidJSON(str string) bool {
	var js json.RawMessage
	return json.Unmarshal([]byte(str), &js) == nil
}

// ParseJSON parses JSON string into interface{}
func ParseJSON(jsonStr string) (interface{}, error) {
	var result interface{}
	err := json.Unmarshal([]byte(jsonStr), &result)
	if err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}
	return result, nil
}

// JSONToMap converts JSON string to map[string]interface{}
func JSONToMap(jsonStr string) (map[string]interface{}, error) {
	var result map[string]interface{}
	err := json.Unmarshal([]byte(jsonStr), &result)
	if err != nil {
		return nil, fmt.Errorf("failed to parse JSON to map: %w", err)
	}
	return result, nil
}

// MapToJSON converts map to JSON string
func MapToJSON(data map[string]interface{}) (string, error) {
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("failed to convert map to JSON: %w", err)
	}
	return string(jsonBytes), nil
}

// ExtractJSONFromText extracts JSON objects from text
func ExtractJSONFromText(text string) []string {
	var jsonObjects []string
	
	// Look for JSON-like patterns
	lines := strings.Split(text, "\n")
	var currentJSON strings.Builder
	inJSON := false
	braceCount := 0
	
	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		
		// Check if line starts a JSON object
		if strings.HasPrefix(trimmed, "{") {
			if !inJSON {
				inJSON = true
				currentJSON.Reset()
			}
		}
		
		if inJSON {
			currentJSON.WriteString(line)
			currentJSON.WriteString("\n")
			
			// Count braces to determine when JSON ends
			for _, char := range line {
				switch char {
				case '{':
					braceCount++
				case '}':
					braceCount--
				}
			}
			
			// If braces are balanced, we have a complete JSON
			if braceCount == 0 {
				jsonStr := strings.TrimSpace(currentJSON.String())
				if IsValidJSON(jsonStr) {
					jsonObjects = append(jsonObjects, jsonStr)
				}
				inJSON = false
				currentJSON.Reset()
			}
		}
	}
	
	return jsonObjects
}

// MergeJSON merges multiple JSON objects
func MergeJSON(jsons ...string) (string, error) {
	result := make(map[string]interface{})
	
	for _, jsonStr := range jsons {
		var data map[string]interface{}
		if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
			return "", fmt.Errorf("failed to parse JSON: %w", err)
		}
		
		// Merge data into result
		for key, value := range data {
			result[key] = value
		}
	}
	
	return MapToJSON(result)
}

// FilterJSONFields filters JSON object to include only specified fields
func FilterJSONFields(jsonStr string, fields []string) (string, error) {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", fmt.Errorf("failed to parse JSON: %w", err)
	}
	
	filtered := make(map[string]interface{})
	for _, field := range fields {
		if value, exists := data[field]; exists {
			filtered[field] = value
		}
	}
	
	return MapToJSON(filtered)
}

// JSONPath extracts value from JSON using simple path notation
func JSONPath(jsonStr, path string) (interface{}, error) {
	var data interface{}
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}
	
	// Split path by dots
	parts := strings.Split(path, ".")
	current := data
	
	for _, part := range parts {
		switch v := current.(type) {
		case map[string]interface{}:
			if value, exists := v[part]; exists {
				current = value
			} else {
				return nil, fmt.Errorf("path not found: %s", path)
			}
		case []interface{}:
			// Handle array indices
			// This is a simplified implementation
			return nil, fmt.Errorf("array indexing not implemented")
		default:
			return nil, fmt.Errorf("cannot navigate path: %s", path)
		}
	}
	
	return current, nil
}

// ValidateJSONSchema performs basic JSON schema validation
func ValidateJSONSchema(jsonStr string, schema map[string]interface{}) error {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return fmt.Errorf("invalid JSON: %w", err)
	}
	
	// Check required fields
	if required, ok := schema["required"].([]interface{}); ok {
		for _, req := range required {
			if reqStr, ok := req.(string); ok {
				if _, exists := data[reqStr]; !exists {
					return fmt.Errorf("missing required field: %s", reqStr)
				}
			}
		}
	}
	
	// Check field types
	if properties, ok := schema["properties"].(map[string]interface{}); ok {
		for field, value := range data {
			if prop, exists := properties[field]; exists {
				if propMap, ok := prop.(map[string]interface{}); ok {
					if expectedType, ok := propMap["type"].(string); ok {
						if !validateJSONType(value, expectedType) {
							return fmt.Errorf("invalid type for field %s: expected %s", field, expectedType)
						}
					}
				}
			}
		}
	}
	
	return nil
}

// validateJSONType validates a value against a JSON schema type
func validateJSONType(value interface{}, expectedType string) bool {
	switch expectedType {
	case "string":
		_, ok := value.(string)
		return ok
	case "number":
		switch value.(type) {
		case int, int32, int64, float32, float64:
			return true
		}
		return false
	case "boolean":
		_, ok := value.(bool)
		return ok
	case "array":
		_, ok := value.([]interface{})
		return ok
	case "object":
		_, ok := value.(map[string]interface{})
		return ok
	case "null":
		return value == nil
	default:
		return true // Unknown type, allow it
	}
}

// EscapeJSONString escapes special characters in JSON strings
func EscapeJSONString(str string) string {
	// Use Go's built-in JSON marshaling for proper escaping
	jsonBytes, _ := json.Marshal(str)
	// Remove the surrounding quotes
	result := string(jsonBytes)
	if len(result) >= 2 && result[0] == '"' && result[len(result)-1] == '"' {
		result = result[1 : len(result)-1]
	}
	return result
}

// UnescapeJSONString unescapes JSON string
func UnescapeJSONString(str string) (string, error) {
	// Add quotes and unmarshal
	quotedStr := `"` + str + `"`
	var result string
	err := json.Unmarshal([]byte(quotedStr), &result)
	return result, err
}
