package cli

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/arien-ai/cli/internal/ai"
	"github.com/arien-ai/cli/internal/ai/providers"
	"github.com/arien-ai/cli/internal/config"
	"github.com/fatih/color"
	"github.com/manifoldco/promptui"
)

// handleSlashCommand handles slash commands
func (i *Interface) handleSlashCommand(ctx context.Context, input string) error {
	parts := strings.Fields(input)
	if len(parts) == 0 {
		return nil
	}

	command := parts[0][1:] // Remove the '/' prefix
	args := parts[1:]

	switch command {
	case "help":
		return i.showHelp()
	case "quit", "exit":
		return i.handleQuit()
	case "config":
		return i.handleConfig(args)
	case "provider":
		return i.handleProvider(ctx, args)
	case "tools":
		return i.handleTools(args)
	case "history":
		return i.handleHistory(args)
	case "clear":
		return i.handleClear()
	case "status":
		return i.handleStatus(ctx)
	case "models":
		return i.handleModels(ctx)
	case "retry":
		return i.handleRetry(ctx, args)
	case "save":
		return i.handleSave(args)
	case "load":
		return i.handleLoad(args)
	default:
		ShowError(fmt.Sprintf("Unknown command: %s", command))
		ShowInfo("Type '/help' for available commands")
		return nil
	}
}

// showHelp displays help information
func (i *Interface) showHelp() error {
	help := `
╔══════════════════════════════════════════════════════════════════════════════╗
║                              ARIEN AI COMMANDS                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

BASIC COMMANDS:
  /help                    Show this help message
  /quit, /exit            Exit the application
  /clear                  Clear the screen
  /status                 Show system status and configuration

CONFIGURATION:
  /config                 Show current configuration
  /config set <key> <val> Set configuration value
  /config reset           Reset to default configuration
  /provider <name>        Switch AI provider (deepseek/ollama)
  /models                 List available models

TOOLS MANAGEMENT:
  /tools                  List all available tools
  /tools enable <name>    Enable a specific tool
  /tools disable <name>   Disable a specific tool
  /tools info <name>      Show detailed tool information

HISTORY & SESSION:
  /history                Show command history
  /history clear          Clear command history
  /history <n>            Show last n commands
  /save <filename>        Save current session
  /load <filename>        Load saved session

ADVANCED:
  /retry                  Retry last failed operation
  /retry <n>              Retry operation n times

USAGE EXAMPLES:
  /provider deepseek      Switch to Deepseek provider
  /tools disable web      Disable web search tool
  /config set ai.deepseek.model deepseek-reasoner
  /history 10             Show last 10 commands
  /save my-session        Save current session

KEYBOARD SHORTCUTS:
  Double ESC              Interrupt ongoing operation
  Ctrl+C                  Exit application

For more information about tools and their usage, type: /tools info <tool-name>
`
	fmt.Print(help)
	return nil
}

// handleQuit handles quit command
func (i *Interface) handleQuit() error {
	ShowInfo("Thank you for using Arien AI!")
	i.isRunning = false
	return nil
}

// handleConfig handles configuration commands
func (i *Interface) handleConfig(args []string) error {
	if len(args) == 0 {
		return i.showConfig()
	}

	switch args[0] {
	case "set":
		if len(args) < 3 {
			ShowError("Usage: /config set <key> <value>")
			return nil
		}
		return i.setConfig(args[1], strings.Join(args[2:], " "))
	case "reset":
		return i.resetConfig()
	default:
		ShowError("Unknown config command. Use: set, reset")
		return nil
	}
}

// showConfig displays current configuration
func (i *Interface) showConfig() error {
	fmt.Println()
	ShowInfo("Current Configuration:")
	fmt.Println()
	
	fmt.Printf("AI Provider: %s\n", ColoredText(i.config.AI.Provider, "highlight"))
	
	if i.config.AI.Provider == "deepseek" {
		fmt.Printf("  Model: %s\n", i.config.AI.Deepseek.Model)
		fmt.Printf("  Base URL: %s\n", i.config.AI.Deepseek.BaseURL)
		fmt.Printf("  API Key: %s\n", maskAPIKey(i.config.AI.Deepseek.APIKey))
	} else if i.config.AI.Provider == "ollama" {
		fmt.Printf("  Model: %s\n", i.config.AI.Ollama.Model)
		fmt.Printf("  Host: %s\n", i.config.AI.Ollama.Host)
	}
	
	fmt.Printf("CLI Theme: %s\n", i.config.CLI.Theme)
	fmt.Printf("Animation: %s\n", boolToString(i.config.CLI.Animation))
	fmt.Printf("Auto Approve: %s\n", boolToString(i.config.CLI.AutoApprove))
	fmt.Printf("Max History: %d\n", i.config.CLI.MaxHistory)
	
	fmt.Printf("Retry Enabled: %s\n", boolToString(i.config.Retry.EnableRetry))
	fmt.Printf("Max Attempts: %d\n", i.config.Retry.MaxAttempts)
	
	fmt.Println()
	return nil
}

// setConfig sets a configuration value
func (i *Interface) setConfig(key, value string) error {
	// This is a simplified implementation
	// In a real implementation, you'd parse the key path and set the value
	ShowInfo(fmt.Sprintf("Setting %s = %s", key, value))
	ShowWarning("Configuration changes will take effect after restart")
	return nil
}

// resetConfig resets configuration to defaults
func (i *Interface) resetConfig() error {
	prompt := promptui.Prompt{
		Label:     "Reset configuration to defaults? (y/N)",
		IsConfirm: true,
	}
	
	result, err := prompt.Run()
	if err != nil {
		return nil // User cancelled
	}
	
	if strings.ToLower(result) == "y" {
		ShowInfo("Configuration reset to defaults")
		ShowWarning("Restart required for changes to take effect")
	}
	
	return nil
}

// handleProvider handles provider switching
func (i *Interface) handleProvider(ctx context.Context, args []string) error {
	if len(args) == 0 {
		ShowInfo(fmt.Sprintf("Current provider: %s", i.config.AI.Provider))
		ShowInfo("Available providers: deepseek, ollama")
		return nil
	}

	newProvider := args[0]
	if newProvider != "deepseek" && newProvider != "ollama" {
		ShowError("Invalid provider. Available: deepseek, ollama")
		return nil
	}

	ShowInfo(fmt.Sprintf("Switching to provider: %s", newProvider))
	ShowWarning("Provider switching requires restart")
	return nil
}

// handleTools handles tool management commands
func (i *Interface) handleTools(args []string) error {
	if len(args) == 0 {
		return i.listTools()
	}

	switch args[0] {
	case "enable":
		if len(args) < 2 {
			ShowError("Usage: /tools enable <tool-name>")
			return nil
		}
		return i.enableTool(args[1])
	case "disable":
		if len(args) < 2 {
			ShowError("Usage: /tools disable <tool-name>")
			return nil
		}
		return i.disableTool(args[1])
	case "info":
		if len(args) < 2 {
			ShowError("Usage: /tools info <tool-name>")
			return nil
		}
		return i.showToolInfo(args[1])
	default:
		ShowError("Unknown tools command. Use: enable, disable, info")
		return nil
	}
}

// listTools lists all available tools
func (i *Interface) listTools() error {
	fmt.Println()
	ShowInfo("Available Tools:")
	fmt.Println()

	allTools := i.toolRegistry.GetAll()
	for name, tool := range allTools {
		status := "disabled"
		statusColor := "error"
		if tool.IsEnabled() {
			status = "enabled"
			statusColor = "success"
		}
		
		fmt.Printf("  %s - %s (%s)\n", 
			ColoredText(name, "highlight"), 
			tool.Description()[:min(60, len(tool.Description()))]+"...",
			ColoredText(status, statusColor))
	}
	
	fmt.Println()
	ShowInfo("Use '/tools info <name>' for detailed information")
	return nil
}

// enableTool enables a specific tool
func (i *Interface) enableTool(name string) error {
	tool, exists := i.toolRegistry.Get(name)
	if !exists {
		ShowError(fmt.Sprintf("Tool not found: %s", name))
		return nil
	}
	
	tool.SetEnabled(true)
	ShowSuccess(fmt.Sprintf("Tool '%s' enabled", name))
	return nil
}

// disableTool disables a specific tool
func (i *Interface) disableTool(name string) error {
	tool, exists := i.toolRegistry.Get(name)
	if !exists {
		ShowError(fmt.Sprintf("Tool not found: %s", name))
		return nil
	}
	
	tool.SetEnabled(false)
	ShowSuccess(fmt.Sprintf("Tool '%s' disabled", name))
	return nil
}

// showToolInfo shows detailed information about a tool
func (i *Interface) showToolInfo(name string) error {
	tool, exists := i.toolRegistry.Get(name)
	if !exists {
		ShowError(fmt.Sprintf("Tool not found: %s", name))
		return nil
	}
	
	fmt.Println()
	fmt.Printf("Tool: %s\n", ColoredText(name, "highlight"))
	fmt.Printf("Status: %s\n", ColoredText(boolToEnabledString(tool.IsEnabled()), "info"))
	fmt.Println()
	fmt.Printf("Description:\n%s\n", tool.Description())
	fmt.Println()
	
	return nil
}

// handleHistory handles history commands
func (i *Interface) handleHistory(args []string) error {
	if len(args) == 0 {
		return i.showHistory(10) // Default to last 10
	}

	switch args[0] {
	case "clear":
		i.history = []string{}
		ShowSuccess("History cleared")
		return nil
	default:
		// Try to parse as number
		if n, err := strconv.Atoi(args[0]); err == nil {
			return i.showHistory(n)
		}
		ShowError("Usage: /history [n] or /history clear")
		return nil
	}
}

// showHistory shows command history
func (i *Interface) showHistory(n int) error {
	if len(i.history) == 0 {
		ShowInfo("No command history")
		return nil
	}

	fmt.Println()
	ShowInfo("Command History:")
	fmt.Println()

	start := max(0, len(i.history)-n)
	for idx, cmd := range i.history[start:] {
		fmt.Printf("%3d: %s\n", start+idx+1, cmd)
	}
	fmt.Println()
	
	return nil
}

// handleClear clears the screen
func (i *Interface) handleClear() error {
	// Clear screen using ANSI escape codes
	fmt.Print("\033[2J\033[H")
	i.printWelcomeBanner()
	return nil
}

// handleStatus shows system status
func (i *Interface) handleStatus(ctx context.Context) error {
	fmt.Println()
	ShowInfo("System Status:")
	fmt.Println()

	// Check AI provider availability
	if err := i.provider.IsAvailable(ctx); err != nil {
		fmt.Printf("AI Provider: %s (%s)\n", 
			ColoredText(i.provider.Name(), "highlight"),
			ColoredText("unavailable", "error"))
		fmt.Printf("Error: %s\n", err.Error())
	} else {
		fmt.Printf("AI Provider: %s (%s)\n", 
			ColoredText(i.provider.Name(), "highlight"),
			ColoredText("available", "success"))
	}

	// Show enabled tools count
	enabledTools := i.toolRegistry.GetEnabled()
	fmt.Printf("Tools: %d enabled, %d total\n", 
		len(enabledTools), 
		len(i.toolRegistry.GetAll()))

	// Show history count
	fmt.Printf("History: %d commands\n", len(i.history))

	fmt.Println()
	return nil
}

// handleModels lists available models
func (i *Interface) handleModels(ctx context.Context) error {
	ShowInfo("Fetching available models...")
	
	models, err := i.provider.GetModels(ctx)
	if err != nil {
		ShowError(fmt.Sprintf("Failed to get models: %v", err))
		return nil
	}

	fmt.Println()
	ShowInfo("Available Models:")
	fmt.Println()

	for _, model := range models {
		fmt.Printf("  %s - %s\n", 
			ColoredText(model.ID, "highlight"),
			model.OwnedBy)
	}
	fmt.Println()

	return nil
}

// handleRetry handles retry commands
func (i *Interface) handleRetry(ctx context.Context, args []string) error {
	if i.lastResult == nil {
		ShowError("No previous operation to retry")
		return nil
	}

	// Parse retry count
	retryCount := 1
	if len(args) > 0 {
		if count, err := strconv.Atoi(args[0]); err == nil && count > 0 {
			retryCount = count
		} else {
			ShowError("Invalid retry count. Must be a positive number.")
			return nil
		}
	}

	ShowInfo(fmt.Sprintf("Retrying last operation %d time(s)...", retryCount))

	// Get the original user message from last result
	originalMessage := i.lastResult.UserMessage
	if originalMessage == "" {
		ShowError("Cannot retry: original message not found")
		return nil
	}

	// Retry the operation
	for attempt := 1; attempt <= retryCount; attempt++ {
		if retryCount > 1 {
			ShowInfo(fmt.Sprintf("Retry attempt %d/%d", attempt, retryCount))
		}

		err := i.processAIRequest(ctx, originalMessage)
		if err == nil {
			ShowSuccess("Retry successful!")
			return nil
		}

		if attempt < retryCount {
			ShowWarning(fmt.Sprintf("Attempt %d failed: %v", attempt, err))
			ShowInfo("Waiting before next attempt...")
			time.Sleep(2 * time.Second)
		} else {
			ShowError(fmt.Sprintf("All retry attempts failed. Last error: %v", err))
		}
	}

	return nil
}

// handleSave handles save session commands
func (i *Interface) handleSave(args []string) error {
	if len(args) == 0 {
		ShowError("Usage: /save <filename>")
		return nil
	}

	filename := args[0]
	if !strings.HasSuffix(filename, ".json") {
		filename += ".json"
	}

	// Create session data
	session := SessionData{
		Timestamp: time.Now(),
		History:   i.history,
		Config:    i.config,
		LastResult: i.lastResult,
	}

	// Save to file
	sessionPath := filepath.Join(i.config.CLI.SessionDir, filename)
	if err := i.saveSession(session, sessionPath); err != nil {
		ShowError(fmt.Sprintf("Failed to save session: %v", err))
		return nil
	}

	ShowSuccess(fmt.Sprintf("Session saved to: %s", sessionPath))
	return nil
}

// handleLoad handles load session commands
func (i *Interface) handleLoad(args []string) error {
	if len(args) == 0 {
		ShowError("Usage: /load <filename>")
		return nil
	}

	filename := args[0]
	if !strings.HasSuffix(filename, ".json") {
		filename += ".json"
	}

	sessionPath := filepath.Join(i.config.CLI.SessionDir, filename)
	session, err := i.loadSession(sessionPath)
	if err != nil {
		ShowError(fmt.Sprintf("Failed to load session: %v", err))
		return nil
	}

	// Restore session data
	i.history = session.History
	i.lastResult = session.LastResult

	ShowSuccess(fmt.Sprintf("Session loaded from: %s", sessionPath))
	ShowInfo(fmt.Sprintf("Restored %d history items", len(i.history)))
	return nil
}

// Helper functions
func maskAPIKey(key string) string {
	if len(key) <= 8 {
		return strings.Repeat("*", len(key))
	}
	return key[:4] + strings.Repeat("*", len(key)-8) + key[len(key)-4:]
}

func boolToString(b bool) string {
	if b {
		return ColoredText("enabled", "success")
	}
	return ColoredText("disabled", "error")
}

func boolToEnabledString(b bool) string {
	if b {
		return "enabled"
	}
	return "disabled"
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// SessionData represents saved session data
type SessionData struct {
	Timestamp  time.Time                `json:"timestamp"`
	History    []string                 `json:"history"`
	Config     *config.Config           `json:"config"`
	LastResult *ai.ProcessResult        `json:"last_result,omitempty"`
}

// saveSession saves session data to file
func (i *Interface) saveSession(session SessionData, path string) error {
	// Create directory if it doesn't exist
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create session directory: %w", err)
	}

	// Marshal to JSON
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session data: %w", err)
	}

	// Write to file
	if err := os.WriteFile(path, data, 0644); err != nil {
		return fmt.Errorf("failed to write session file: %w", err)
	}

	return nil
}

// loadSession loads session data from file
func (i *Interface) loadSession(path string) (*SessionData, error) {
	// Check if file exists
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return nil, fmt.Errorf("session file not found: %s", path)
	}

	// Read file
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read session file: %w", err)
	}

	// Unmarshal JSON
	var session SessionData
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, fmt.Errorf("failed to unmarshal session data: %w", err)
	}

	return &session, nil
}
