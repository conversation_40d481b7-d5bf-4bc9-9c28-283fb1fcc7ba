package ai

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/arien-ai/cli/internal/config"
)

// RetryableError represents an error that can be retried
type RetryableError struct {
	Err       error
	Retryable bool
	Temporary bool
}

func (e *RetryableError) Error() string {
	return e.Err.Error()
}

func (e *RetryableError) Unwrap() error {
	return e.Err
}

// IsRetryable checks if an error is retryable
func IsRetryable(err error) bool {
	if retryableErr, ok := err.(*RetryableError); ok {
		return retryableErr.Retryable
	}
	
	// Check for common retryable errors
	errStr := err.Error()
	
	// Network-related errors
	if contains(errStr, "timeout") ||
		contains(errStr, "connection refused") ||
		contains(errStr, "connection reset") ||
		contains(errStr, "network is unreachable") ||
		contains(errStr, "temporary failure") {
		return true
	}
	
	// Rate limiting
	if contains(errStr, "rate limit") ||
		contains(errStr, "too many requests") ||
		contains(errStr, "429") {
		return true
	}
	
	// Server errors
	if contains(errStr, "500") ||
		contains(errStr, "502") ||
		contains(errStr, "503") ||
		contains(errStr, "504") {
		return true
	}
	
	return false
}

// RetryConfig contains retry configuration
type RetryConfig struct {
	MaxAttempts   int
	InitialDelay  time.Duration
	MaxDelay      time.Duration
	BackoffFactor float64
	EnableRetry   bool
}

// RetryManager handles retry logic with exponential backoff
type RetryManager struct {
	config *config.RetryConfig
}

// NewRetryManager creates a new retry manager
func NewRetryManager(cfg *config.RetryConfig) *RetryManager {
	return &RetryManager{
		config: cfg,
	}
}

// RetryFunc represents a function that can be retried
type RetryFunc func(ctx context.Context, attempt int) error

// Execute executes a function with retry logic
func (r *RetryManager) Execute(ctx context.Context, operation string, fn RetryFunc) error {
	if !r.config.EnableRetry {
		return fn(ctx, 1)
	}

	var lastErr error
	
	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		// Check context cancellation
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// Execute the function
		err := fn(ctx, attempt)
		if err == nil {
			return nil // Success
		}

		lastErr = err

		// Check if error is retryable
		if !IsRetryable(err) {
			return fmt.Errorf("non-retryable error in %s: %w", operation, err)
		}

		// Don't retry on last attempt
		if attempt == r.config.MaxAttempts {
			break
		}

		// Calculate delay with exponential backoff
		delay := r.calculateDelay(attempt)
		
		// Log retry attempt
		fmt.Printf("Attempt %d/%d failed for %s: %v. Retrying in %v...\n", 
			attempt, r.config.MaxAttempts, operation, err, delay)

		// Wait before retry
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}

	return fmt.Errorf("operation %s failed after %d attempts: %w", 
		operation, r.config.MaxAttempts, lastErr)
}

// ExecuteWithCallback executes a function with retry logic and progress callback
func (r *RetryManager) ExecuteWithCallback(ctx context.Context, operation string, fn RetryFunc, callback func(attempt, maxAttempts int, err error)) error {
	if !r.config.EnableRetry {
		err := fn(ctx, 1)
		if callback != nil {
			callback(1, 1, err)
		}
		return err
	}

	var lastErr error
	
	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		// Check context cancellation
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// Execute the function
		err := fn(ctx, attempt)
		
		// Call progress callback
		if callback != nil {
			callback(attempt, r.config.MaxAttempts, err)
		}

		if err == nil {
			return nil // Success
		}

		lastErr = err

		// Check if error is retryable
		if !IsRetryable(err) {
			return fmt.Errorf("non-retryable error in %s: %w", operation, err)
		}

		// Don't retry on last attempt
		if attempt == r.config.MaxAttempts {
			break
		}

		// Calculate delay with exponential backoff
		delay := r.calculateDelay(attempt)

		// Wait before retry
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}

	return fmt.Errorf("operation %s failed after %d attempts: %w", 
		operation, r.config.MaxAttempts, lastErr)
}

// calculateDelay calculates the delay for the next retry attempt
func (r *RetryManager) calculateDelay(attempt int) time.Duration {
	// Convert milliseconds to duration
	initialDelay := time.Duration(r.config.InitialDelay) * time.Millisecond
	maxDelay := time.Duration(r.config.MaxDelay) * time.Millisecond
	
	// Calculate exponential backoff
	delay := float64(initialDelay) * math.Pow(r.config.BackoffFactor, float64(attempt-1))
	
	// Apply jitter (±25% randomization)
	jitter := 0.25
	minDelay := delay * (1 - jitter)
	maxDelayFloat := delay * (1 + jitter)
	
	// Random jitter
	actualDelay := time.Duration(minDelay + (maxDelayFloat-minDelay)*0.5) // Simplified jitter
	
	// Cap at max delay
	if actualDelay > maxDelay {
		actualDelay = maxDelay
	}
	
	return actualDelay
}

// RetryableOperation wraps an operation with retry logic
type RetryableOperation struct {
	Name        string
	Operation   RetryFunc
	RetryConfig *config.RetryConfig
}

// NewRetryableOperation creates a new retryable operation
func NewRetryableOperation(name string, operation RetryFunc, cfg *config.RetryConfig) *RetryableOperation {
	return &RetryableOperation{
		Name:        name,
		Operation:   operation,
		RetryConfig: cfg,
	}
}

// Execute executes the retryable operation
func (ro *RetryableOperation) Execute(ctx context.Context) error {
	retryManager := NewRetryManager(ro.RetryConfig)
	return retryManager.Execute(ctx, ro.Name, ro.Operation)
}

// ExecuteWithProgress executes the retryable operation with progress reporting
func (ro *RetryableOperation) ExecuteWithProgress(ctx context.Context, progressCallback func(attempt, maxAttempts int, err error)) error {
	retryManager := NewRetryManager(ro.RetryConfig)
	return retryManager.ExecuteWithCallback(ctx, ro.Name, ro.Operation, progressCallback)
}

// CircuitBreaker implements circuit breaker pattern for additional resilience
type CircuitBreaker struct {
	maxFailures   int
	resetTimeout  time.Duration
	failures      int
	lastFailTime  time.Time
	state         CircuitState
}

// CircuitState represents the state of a circuit breaker
type CircuitState int

const (
	CircuitClosed CircuitState = iota
	CircuitOpen
	CircuitHalfOpen
)

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(maxFailures int, resetTimeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		maxFailures:  maxFailures,
		resetTimeout: resetTimeout,
		state:        CircuitClosed,
	}
}

// Execute executes a function through the circuit breaker
func (cb *CircuitBreaker) Execute(ctx context.Context, fn func() error) error {
	// Check if circuit should be reset
	if cb.state == CircuitOpen && time.Since(cb.lastFailTime) > cb.resetTimeout {
		cb.state = CircuitHalfOpen
		cb.failures = 0
	}

	// Reject if circuit is open
	if cb.state == CircuitOpen {
		return fmt.Errorf("circuit breaker is open")
	}

	// Execute function
	err := fn()
	
	if err != nil {
		cb.failures++
		cb.lastFailTime = time.Now()
		
		// Open circuit if max failures reached
		if cb.failures >= cb.maxFailures {
			cb.state = CircuitOpen
		}
		
		return err
	}

	// Reset on success
	if cb.state == CircuitHalfOpen {
		cb.state = CircuitClosed
	}
	cb.failures = 0
	
	return nil
}

// GetState returns the current circuit breaker state
func (cb *CircuitBreaker) GetState() CircuitState {
	return cb.state
}

// Reset resets the circuit breaker
func (cb *CircuitBreaker) Reset() {
	cb.state = CircuitClosed
	cb.failures = 0
}

// Helper function to check if string contains substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr ||
		      containsInner(s, substr))))
}

func containsInner(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
