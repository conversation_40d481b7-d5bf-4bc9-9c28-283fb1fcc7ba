package tools

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/arien-ai/cli/internal/ai/providers"
)

// EditTool implements file editing functionality
type EditTool struct {
	*BaseTool
}

// NewEditTool creates a new edit tool
func NewEditTool() *EditTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"path": map[string]interface{}{
				"type":        "string",
				"description": "File path to edit. Must be an existing file.",
			},
			"operation": map[string]interface{}{
				"type":        "string",
				"description": "Edit operation: 'replace', 'insert', 'delete', 'append', 'prepend'.",
				"enum":        []string{"replace", "insert", "delete", "append", "prepend"},
			},
			"search": map[string]interface{}{
				"type":        "string",
				"description": "Text to search for (required for replace and delete operations). Supports regex.",
			},
			"replacement": map[string]interface{}{
				"type":        "string",
				"description": "Replacement text (required for replace and insert operations).",
			},
			"line_number": map[string]interface{}{
				"type":        "number",
				"description": "Line number for insert/delete operations (1-based). Optional for other operations.",
			},
			"regex": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether search text is a regular expression. Defaults to false.",
			},
			"case_sensitive": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether search should be case sensitive. Defaults to true.",
			},
			"all_occurrences": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether to replace all occurrences or just the first. Defaults to false (first only).",
			},
			"backup": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether to create a backup before editing. Defaults to true.",
			},
		},
		"required": []string{"path", "operation"},
	}

	description := `Edits files by replacing text, creating new files, or deleting content.

USAGE GUIDELINES:
- Performs precise edits on existing files using various operations
- Supports text replacement, insertion, deletion, and line-based operations
- Uses regular expressions for complex pattern matching
- Creates automatic backups to prevent data loss
- Provides detailed feedback about changes made

WHEN TO USE:
- Making specific changes to existing files
- Replacing configuration values or code snippets
- Inserting new content at specific locations
- Deleting unwanted lines or sections
- Updating file headers or footers

WHEN NOT TO USE:
- For creating entirely new files (use write tool instead)
- For moving or renaming files (use bash tool with mv)
- For large file rewrites (use write tool to overwrite)
- For binary file modifications

EDIT OPERATIONS:
- 'replace': Replace search text with replacement text
- 'insert': Insert text at specified line number or after search text
- 'delete': Delete lines containing search text or at line number
- 'append': Add text to the end of the file
- 'prepend': Add text to the beginning of the file

EXAMPLES:
- Replace text: {"path": "config.yaml", "operation": "replace", "search": "old_value", "replacement": "new_value"}
- Insert line: {"path": "script.sh", "operation": "insert", "line_number": 5, "replacement": "echo 'Hello'"}
- Delete lines: {"path": "log.txt", "operation": "delete", "search": "ERROR", "regex": false}
- Regex replace: {"path": "code.go", "operation": "replace", "search": "func\\s+(\\w+)", "replacement": "func New$1", "regex": true}
- Append content: {"path": "notes.txt", "operation": "append", "replacement": "\\nNew note"}

SAFETY FEATURES:
- Automatic backup creation before modifications
- Validation of file existence and permissions
- Preview of changes before applying
- Error handling for invalid operations
- Support for undoing changes via backups

REGEX SUPPORT:
- Use regex: true for pattern-based replacements
- Supports capture groups with $1, $2, etc.
- Case-insensitive matching with case_sensitive: false
- Global replacements with all_occurrences: true`

	return &EditTool{
		BaseTool: NewBaseTool("edit", description, parameters),
	}
}

// Execute executes the file edit operation
func (t *EditTool) Execute(ctx context.Context, args map[string]interface{}) (*providers.ToolResult, error) {
	// Validate arguments
	if err := t.ValidateArgs(args); err != nil {
		return providers.NewErrorToolResult("", err), nil
	}

	filePath, ok := args["path"].(string)
	if !ok {
		return providers.NewErrorToolResult("", fmt.Errorf("path must be a string")), nil
	}

	operation, ok := args["operation"].(string)
	if !ok {
		return providers.NewErrorToolResult("", fmt.Errorf("operation must be a string")), nil
	}

	// Get optional parameters
	search := ""
	if s, ok := args["search"].(string); ok {
		search = s
	}

	replacement := ""
	if r, ok := args["replacement"].(string); ok {
		replacement = r
	}

	lineNumber := 0
	if ln, ok := args["line_number"].(float64); ok {
		lineNumber = int(ln)
	}

	useRegex := false
	if r, ok := args["regex"].(bool); ok {
		useRegex = r
	}

	caseSensitive := true
	if cs, ok := args["case_sensitive"].(bool); ok {
		caseSensitive = cs
	}

	allOccurrences := false
	if ao, ok := args["all_occurrences"].(bool); ok {
		allOccurrences = ao
	}

	backup := true
	if b, ok := args["backup"].(bool); ok {
		backup = b
	}

	// Validate file exists
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return providers.NewErrorToolResult("", fmt.Errorf("invalid file path: %w", err)), nil
	}

	if _, err := os.Stat(absPath); os.IsNotExist(err) {
		return providers.NewErrorToolResult("", fmt.Errorf("file does not exist: %s", absPath)), nil
	}

	// Validate operation-specific requirements
	if err := t.validateOperation(operation, search, replacement, lineNumber); err != nil {
		return providers.NewErrorToolResult("", err), nil
	}

	// Read original file
	originalContent, err := t.readFile(absPath)
	if err != nil {
		return providers.NewErrorToolResult("", fmt.Errorf("failed to read file: %w", err)), nil
	}

	// Create backup if requested
	var backupPath string
	if backup {
		backupPath = absPath + ".backup." + time.Now().Format("20060102-150405")
		if err := t.createBackup(absPath, backupPath); err != nil {
			return providers.NewErrorToolResult("", fmt.Errorf("failed to create backup: %w", err)), nil
		}
	}

	// Perform edit operation
	newContent, changes, err := t.performEdit(originalContent, operation, search, replacement, lineNumber, useRegex, caseSensitive, allOccurrences)
	if err != nil {
		return providers.NewErrorToolResult("", fmt.Errorf("edit operation failed: %w", err)), nil
	}

	// Write modified content back to file
	if err := t.writeFile(absPath, newContent); err != nil {
		return providers.NewErrorToolResult("", fmt.Errorf("failed to write file: %w", err)), nil
	}

	// Get final file info
	finalInfo, err := os.Stat(absPath)
	if err != nil {
		return providers.NewErrorToolResult("", fmt.Errorf("failed to get file info: %w", err)), nil
	}

	// Prepare result
	result := &providers.ToolResult{
		Success: true,
		Data: map[string]interface{}{
			"path":            absPath,
			"operation":       operation,
			"changes_made":    len(changes),
			"changes":         changes,
			"backup_created":  backup,
			"backup_path":     backupPath,
			"file_size":       finalInfo.Size(),
			"modified_time":   finalInfo.ModTime(),
			"original_lines":  strings.Count(originalContent, "\n") + 1,
			"new_lines":       strings.Count(newContent, "\n") + 1,
			"operation_details": map[string]interface{}{
				"search":           search,
				"replacement":      replacement,
				"line_number":      lineNumber,
				"regex":            useRegex,
				"case_sensitive":   caseSensitive,
				"all_occurrences":  allOccurrences,
			},
		},
	}

	if len(changes) == 0 {
		result.Content = fmt.Sprintf("No changes made to file '%s' - no matches found", absPath)
	} else {
		result.Content = fmt.Sprintf("Successfully edited file '%s' - made %d changes", absPath, len(changes))
	}

	if backup {
		result.Content += fmt.Sprintf(". Backup created at '%s'", backupPath)
	}

	return result, nil
}

// Change represents a single edit change
type Change struct {
	Type        string `json:"type"`
	LineNumber  int    `json:"line_number"`
	OldContent  string `json:"old_content"`
	NewContent  string `json:"new_content"`
	Description string `json:"description"`
}

// validateOperation validates operation-specific requirements
func (t *EditTool) validateOperation(operation, search, replacement string, lineNumber int) error {
	switch operation {
	case "replace":
		if search == "" {
			return fmt.Errorf("search text is required for replace operation")
		}
		if replacement == "" {
			return fmt.Errorf("replacement text is required for replace operation")
		}
	case "insert":
		if replacement == "" {
			return fmt.Errorf("replacement text is required for insert operation")
		}
		if lineNumber == 0 && search == "" {
			return fmt.Errorf("either line_number or search text is required for insert operation")
		}
	case "delete":
		if search == "" && lineNumber == 0 {
			return fmt.Errorf("either search text or line_number is required for delete operation")
		}
	case "append", "prepend":
		if replacement == "" {
			return fmt.Errorf("replacement text is required for %s operation", operation)
		}
	default:
		return fmt.Errorf("unsupported operation: %s", operation)
	}
	return nil
}

// performEdit performs the actual edit operation
func (t *EditTool) performEdit(content, operation, search, replacement string, lineNumber int, useRegex, caseSensitive, allOccurrences bool) (string, []Change, error) {
	var changes []Change
	
	switch operation {
	case "replace":
		return t.performReplace(content, search, replacement, useRegex, caseSensitive, allOccurrences)
	case "insert":
		return t.performInsert(content, search, replacement, lineNumber, useRegex, caseSensitive)
	case "delete":
		return t.performDelete(content, search, lineNumber, useRegex, caseSensitive, allOccurrences)
	case "append":
		return t.performAppend(content, replacement)
	case "prepend":
		return t.performPrepend(content, replacement)
	default:
		return content, changes, fmt.Errorf("unsupported operation: %s", operation)
	}
}

// performReplace performs text replacement
func (t *EditTool) performReplace(content, search, replacement string, useRegex, caseSensitive, allOccurrences bool) (string, []Change, error) {
	var changes []Change
	
	if useRegex {
		var regex *regexp.Regexp
		var err error
		
		if caseSensitive {
			regex, err = regexp.Compile(search)
		} else {
			regex, err = regexp.Compile("(?i)" + search)
		}
		
		if err != nil {
			return content, changes, fmt.Errorf("invalid regex: %w", err)
		}
		
		if allOccurrences {
			newContent := regex.ReplaceAllString(content, replacement)
			if newContent != content {
				changes = append(changes, Change{
					Type:        "regex_replace_all",
					Description: fmt.Sprintf("Replaced all occurrences of regex '%s' with '%s'", search, replacement),
					OldContent:  content,
					NewContent:  newContent,
				})
			}
			return newContent, changes, nil
		} else {
			newContent := regex.ReplaceAllString(content, replacement)
			if newContent != content {
				changes = append(changes, Change{
					Type:        "regex_replace_first",
					Description: fmt.Sprintf("Replaced first occurrence of regex '%s' with '%s'", search, replacement),
					OldContent:  content,
					NewContent:  newContent,
				})
			}
			return newContent, changes, nil
		}
	} else {
		// Simple string replacement
		searchText := search
		if !caseSensitive {
			searchText = strings.ToLower(search)
			content = strings.ToLower(content)
		}
		
		if allOccurrences {
			newContent := strings.ReplaceAll(content, searchText, replacement)
			if newContent != content {
				changes = append(changes, Change{
					Type:        "string_replace_all",
					Description: fmt.Sprintf("Replaced all occurrences of '%s' with '%s'", search, replacement),
					OldContent:  content,
					NewContent:  newContent,
				})
			}
			return newContent, changes, nil
		} else {
			newContent := strings.Replace(content, searchText, replacement, 1)
			if newContent != content {
				changes = append(changes, Change{
					Type:        "string_replace_first",
					Description: fmt.Sprintf("Replaced first occurrence of '%s' with '%s'", search, replacement),
					OldContent:  content,
					NewContent:  newContent,
				})
			}
			return newContent, changes, nil
		}
	}
}

// performInsert performs text insertion
func (t *EditTool) performInsert(content, search, replacement string, lineNumber int, useRegex, caseSensitive bool) (string, []Change, error) {
	lines := strings.Split(content, "\n")
	var changes []Change
	
	if lineNumber > 0 {
		// Insert at specific line number
		if lineNumber > len(lines)+1 {
			return content, changes, fmt.Errorf("line number %d exceeds file length %d", lineNumber, len(lines))
		}
		
		insertIndex := lineNumber - 1
		newLines := make([]string, 0, len(lines)+1)
		newLines = append(newLines, lines[:insertIndex]...)
		newLines = append(newLines, replacement)
		newLines = append(newLines, lines[insertIndex:]...)
		
		changes = append(changes, Change{
			Type:        "insert_line",
			LineNumber:  lineNumber,
			Description: fmt.Sprintf("Inserted text at line %d", lineNumber),
			NewContent:  replacement,
		})
		
		return strings.Join(newLines, "\n"), changes, nil
	} else if search != "" {
		// Insert after search text
		// Implementation for search-based insertion
		// This is a simplified version - you might want to enhance it
		newContent := strings.Replace(content, search, search+"\n"+replacement, 1)
		if newContent != content {
			changes = append(changes, Change{
				Type:        "insert_after_search",
				Description: fmt.Sprintf("Inserted text after '%s'", search),
				NewContent:  replacement,
			})
		}
		return newContent, changes, nil
	}
	
	return content, changes, fmt.Errorf("either line_number or search text must be provided for insert operation")
}

// performDelete performs text deletion
func (t *EditTool) performDelete(content, search string, lineNumber int, useRegex, caseSensitive, allOccurrences bool) (string, []Change, error) {
	var changes []Change
	
	if lineNumber > 0 {
		// Delete specific line
		lines := strings.Split(content, "\n")
		if lineNumber > len(lines) {
			return content, changes, fmt.Errorf("line number %d exceeds file length %d", lineNumber, len(lines))
		}
		
		deletedLine := lines[lineNumber-1]
		newLines := append(lines[:lineNumber-1], lines[lineNumber:]...)
		
		changes = append(changes, Change{
			Type:        "delete_line",
			LineNumber:  lineNumber,
			Description: fmt.Sprintf("Deleted line %d", lineNumber),
			OldContent:  deletedLine,
		})
		
		return strings.Join(newLines, "\n"), changes, nil
	} else if search != "" {
		// Delete lines containing search text
		lines := strings.Split(content, "\n")
		var newLines []string
		
		for i, line := range lines {
			shouldDelete := false
			
			if useRegex {
				var regex *regexp.Regexp
				var err error
				
				if caseSensitive {
					regex, err = regexp.Compile(search)
				} else {
					regex, err = regexp.Compile("(?i)" + search)
				}
				
				if err != nil {
					return content, changes, fmt.Errorf("invalid regex: %w", err)
				}
				
				shouldDelete = regex.MatchString(line)
			} else {
				searchText := search
				lineText := line
				if !caseSensitive {
					searchText = strings.ToLower(search)
					lineText = strings.ToLower(line)
				}
				shouldDelete = strings.Contains(lineText, searchText)
			}
			
			if shouldDelete {
				changes = append(changes, Change{
					Type:        "delete_matching_line",
					LineNumber:  i + 1,
					Description: fmt.Sprintf("Deleted line %d containing '%s'", i+1, search),
					OldContent:  line,
				})
				
				if !allOccurrences {
					// Only delete first occurrence
					newLines = append(newLines, lines[i+1:]...)
					break
				}
			} else {
				newLines = append(newLines, line)
			}
		}
		
		return strings.Join(newLines, "\n"), changes, nil
	}
	
	return content, changes, fmt.Errorf("either line_number or search text must be provided for delete operation")
}

// performAppend appends text to the end of file
func (t *EditTool) performAppend(content, replacement string) (string, []Change, error) {
	newContent := content + replacement
	changes := []Change{
		{
			Type:        "append",
			Description: "Appended text to end of file",
			NewContent:  replacement,
		},
	}
	return newContent, changes, nil
}

// performPrepend prepends text to the beginning of file
func (t *EditTool) performPrepend(content, replacement string) (string, []Change, error) {
	newContent := replacement + content
	changes := []Change{
		{
			Type:        "prepend",
			Description: "Prepended text to beginning of file",
			NewContent:  replacement,
		},
	}
	return newContent, changes, nil
}

// readFile reads the entire file content
func (t *EditTool) readFile(path string) (string, error) {
	content, err := os.ReadFile(path)
	if err != nil {
		return "", err
	}
	return string(content), nil
}

// writeFile writes content to file
func (t *EditTool) writeFile(path, content string) error {
	return os.WriteFile(path, []byte(content), 0644)
}

// createBackup creates a backup of the original file
func (t *EditTool) createBackup(originalPath, backupPath string) error {
	content, err := os.ReadFile(originalPath)
	if err != nil {
		return err
	}
	return os.WriteFile(backupPath, content, 0644)
}
