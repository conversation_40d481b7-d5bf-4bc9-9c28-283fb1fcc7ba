package tools

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/arien-ai/cli/internal/ai/providers"
)

// WriteTool implements file writing functionality
type WriteTool struct {
	*BaseTool
}

// NewWriteTool creates a new write tool
func NewWriteTool() *WriteTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"path": map[string]interface{}{
				"type":        "string",
				"description": "File path to write to. Can be absolute or relative to current directory.",
			},
			"content": map[string]interface{}{
				"type":        "string",
				"description": "Content to write to the file. Use \\n for line breaks.",
			},
			"mode": map[string]interface{}{
				"type":        "string",
				"description": "Write mode: 'create' (fail if exists), 'overwrite', or 'append'. Defaults to 'overwrite'.",
				"enum":        []string{"create", "overwrite", "append"},
			},
			"create_dirs": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether to create parent directories if they don't exist. Defaults to true.",
			},
			"permissions": map[string]interface{}{
				"type":        "string",
				"description": "File permissions in octal format (e.g., '0644'). Defaults to '0644'.",
			},
			"backup": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether to create a backup of existing file before overwriting. Defaults to false.",
			},
			"encoding": map[string]interface{}{
				"type":        "string",
				"description": "Text encoding: 'utf-8', 'ascii'. Defaults to 'utf-8'.",
				"enum":        []string{"utf-8", "ascii"},
			},
		},
		"required": []string{"path", "content"},
	}

	description := `File writing tool that creates or updates files in the filesystem.

USAGE GUIDELINES:
- Creates new files or updates existing files with specified content
- Supports different write modes: create, overwrite, or append
- Automatically creates parent directories when needed
- Provides backup functionality for existing files
- Handles text encoding and file permissions

WHEN TO USE:
- Creating new configuration files, scripts, or documents
- Writing generated code, logs, or data files
- Saving processed content or analysis results
- Creating temporary files for processing
- Updating existing files with new content

WHEN NOT TO USE:
- For making small edits to existing files (use edit tool instead)
- For binary file operations (limited to text files)
- For very large files (may cause memory issues)
- For system files requiring special permissions

WRITE MODES:
- 'create': Creates new file, fails if file already exists
- 'overwrite': Replaces existing file content completely
- 'append': Adds content to the end of existing file

EXAMPLES:
- Create new file: {"path": "config.yaml", "content": "key: value\\n", "mode": "create"}
- Overwrite file: {"path": "output.txt", "content": "New content", "mode": "overwrite"}
- Append to log: {"path": "app.log", "content": "Error message\\n", "mode": "append"}
- With backup: {"path": "important.txt", "content": "Updated", "backup": true}
- Create directories: {"path": "new/dir/file.txt", "content": "Hello", "create_dirs": true}

SAFETY FEATURES:
- Backup option prevents data loss
- Create mode prevents accidental overwrites
- Directory creation ensures proper file structure
- Permission control for security
- Validation of file paths and content

BEST PRACTICES:
- Use create mode for new files to avoid accidents
- Enable backup for important file modifications
- Set appropriate permissions for security
- Use absolute paths when possible for clarity`

	return &WriteTool{
		BaseTool: NewBaseTool("write", description, parameters),
	}
}

// Execute executes the file write operation
func (t *WriteTool) Execute(ctx context.Context, args map[string]interface{}) (*providers.ToolResult, error) {
	// Validate arguments
	if err := t.ValidateArgs(args); err != nil {
		return providers.NewErrorToolResult("", err), nil
	}

	filePath, ok := args["path"].(string)
	if !ok {
		return providers.NewErrorToolResult("", fmt.Errorf("path must be a string")), nil
	}

	content, ok := args["content"].(string)
	if !ok {
		return providers.NewErrorToolResult("", fmt.Errorf("content must be a string")), nil
	}

	// Get optional parameters
	mode := "overwrite"
	if m, ok := args["mode"].(string); ok {
		mode = m
	}

	createDirs := true
	if cd, ok := args["create_dirs"].(bool); ok {
		createDirs = cd
	}

	permissions := "0644"
	if p, ok := args["permissions"].(string); ok {
		permissions = p
	}

	backup := false
	if b, ok := args["backup"].(bool); ok {
		backup = b
	}

	encoding := "utf-8"
	if e, ok := args["encoding"].(string); ok {
		encoding = e
	}

	// Validate and normalize path
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return providers.NewErrorToolResult("", fmt.Errorf("invalid file path: %w", err)), nil
	}

	// Check if file exists
	fileExists := false
	if _, err := os.Stat(absPath); err == nil {
		fileExists = true
	}

	// Handle create mode
	if mode == "create" && fileExists {
		return providers.NewErrorToolResult("", fmt.Errorf("file already exists: %s", absPath)), nil
	}

	// Create parent directories if needed
	if createDirs {
		parentDir := filepath.Dir(absPath)
		if err := os.MkdirAll(parentDir, 0755); err != nil {
			return providers.NewErrorToolResult("", fmt.Errorf("failed to create directories: %w", err)), nil
		}
	}

	// Create backup if requested and file exists
	var backupPath string
	if backup && fileExists {
		backupPath = absPath + ".backup." + time.Now().Format("20060102-150405")
		if err := t.copyFile(absPath, backupPath); err != nil {
			return providers.NewErrorToolResult("", fmt.Errorf("failed to create backup: %w", err)), nil
		}
	}

	// Parse permissions
	perm, err := t.parsePermissions(permissions)
	if err != nil {
		return providers.NewErrorToolResult("", fmt.Errorf("invalid permissions: %w", err)), nil
	}

	// Validate encoding
	if encoding != "utf-8" && encoding != "ascii" {
		return providers.NewErrorToolResult("", fmt.Errorf("unsupported encoding: %s", encoding)), nil
	}

	// Validate content for ASCII encoding
	if encoding == "ascii" && !t.isASCII(content) {
		return providers.NewErrorToolResult("", fmt.Errorf("content contains non-ASCII characters")), nil
	}

	// Write file based on mode
	var writeErr error
	var bytesWritten int

	switch mode {
	case "create", "overwrite":
		bytesWritten, writeErr = t.writeFile(absPath, content, perm)
	case "append":
		bytesWritten, writeErr = t.appendFile(absPath, content, perm)
	default:
		return providers.NewErrorToolResult("", fmt.Errorf("invalid write mode: %s", mode)), nil
	}

	if writeErr != nil {
		return providers.NewErrorToolResult("", fmt.Errorf("failed to write file: %w", writeErr)), nil
	}

	// Get final file info
	finalInfo, err := os.Stat(absPath)
	if err != nil {
		return providers.NewErrorToolResult("", fmt.Errorf("failed to get file info: %w", err)), nil
	}

	// Prepare result
	result := &providers.ToolResult{
		Success: true,
		Data: map[string]interface{}{
			"path":           absPath,
			"mode":           mode,
			"bytes_written":  bytesWritten,
			"file_size":      finalInfo.Size(),
			"permissions":    finalInfo.Mode().String(),
			"modified_time":  finalInfo.ModTime(),
			"created_dirs":   createDirs,
			"backup_created": backup && backupPath != "",
			"backup_path":    backupPath,
			"encoding":       encoding,
			"operation_details": map[string]interface{}{
				"file_existed":    fileExists,
				"content_length":  len(content),
				"line_count":      strings.Count(content, "\n") + 1,
			},
		},
	}

	// Generate content summary
	contentSummary := t.generateContentSummary(content)
	
	switch mode {
	case "create":
		result.Content = fmt.Sprintf("Created new file '%s' with %d bytes (%s)", absPath, bytesWritten, contentSummary)
	case "overwrite":
		result.Content = fmt.Sprintf("Overwrote file '%s' with %d bytes (%s)", absPath, bytesWritten, contentSummary)
	case "append":
		result.Content = fmt.Sprintf("Appended %d bytes to file '%s' (%s)", bytesWritten, absPath, contentSummary)
	}

	if backup && backupPath != "" {
		result.Content += fmt.Sprintf(". Backup created at '%s'", backupPath)
	}

	return result, nil
}

// writeFile writes content to a file (create/overwrite mode)
func (t *WriteTool) writeFile(path, content string, perm os.FileMode) (int, error) {
	file, err := os.OpenFile(path, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, perm)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	return file.WriteString(content)
}

// appendFile appends content to a file
func (t *WriteTool) appendFile(path, content string, perm os.FileMode) (int, error) {
	file, err := os.OpenFile(path, os.O_CREATE|os.O_WRONLY|os.O_APPEND, perm)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	return file.WriteString(content)
}

// copyFile creates a copy of a file
func (t *WriteTool) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	buffer := make([]byte, 4096)
	for {
		n, err := sourceFile.Read(buffer)
		if n > 0 {
			if _, writeErr := destFile.Write(buffer[:n]); writeErr != nil {
				return writeErr
			}
		}
		if err != nil {
			break
		}
	}

	return nil
}

// parsePermissions parses permission string to os.FileMode
func (t *WriteTool) parsePermissions(permStr string) (os.FileMode, error) {
	// Default permissions
	if permStr == "" {
		return 0644, nil
	}

	// Parse octal permissions
	var perm uint32
	if _, err := fmt.Sscanf(permStr, "%o", &perm); err != nil {
		return 0, fmt.Errorf("invalid permission format: %s", permStr)
	}

	return os.FileMode(perm), nil
}

// isASCII checks if string contains only ASCII characters
func (t *WriteTool) isASCII(s string) bool {
	for _, r := range s {
		if r > 127 {
			return false
		}
	}
	return true
}

// generateContentSummary generates a summary of the content
func (t *WriteTool) generateContentSummary(content string) string {
	lines := strings.Count(content, "\n") + 1
	chars := len(content)
	
	summary := fmt.Sprintf("%d lines, %d characters", lines, chars)
	
	// Add content type hint
	if strings.Contains(content, "{") && strings.Contains(content, "}") {
		summary += ", JSON-like"
	} else if strings.Contains(content, "<") && strings.Contains(content, ">") {
		summary += ", XML/HTML-like"
	} else if strings.Contains(content, "#!/") {
		summary += ", script"
	} else if strings.Contains(content, "package ") || strings.Contains(content, "import ") {
		summary += ", code"
	}
	
	return summary
}
