package providers

import (
	"context"
	"encoding/json"
	"fmt"
)

// Provider defines the interface for AI providers
type Provider interface {
	// Name returns the provider name
	Name() string
	
	// Chat sends a message and returns the response
	Chat(ctx context.Context, request *ChatRequest) (*ChatResponse, error)
	
	// ChatStream sends a message and returns a streaming response
	ChatStream(ctx context.Context, request *ChatRequest) (<-chan *ChatStreamResponse, error)
	
	// IsAvailable checks if the provider is available
	IsAvailable(ctx context.Context) error
	
	// GetModels returns available models
	GetModels(ctx context.Context) ([]Model, error)
}

// ChatRequest represents a chat request
type ChatRequest struct {
	Model       string     `json:"model"`
	Messages    []Message  `json:"messages"`
	Tools       []Tool     `json:"tools,omitempty"`
	ToolChoice  string     `json:"tool_choice,omitempty"`
	Temperature float64    `json:"temperature,omitempty"`
	MaxTokens   int        `json:"max_tokens,omitempty"`
	Stream      bool       `json:"stream,omitempty"`
}

// ChatResponse represents a chat response
type ChatResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage"`
}

// ChatStreamResponse represents a streaming chat response
type ChatStreamResponse struct {
	ID      string        `json:"id"`
	Object  string        `json:"object"`
	Created int64         `json:"created"`
	Model   string        `json:"model"`
	Choices []StreamChoice `json:"choices"`
	Done    bool          `json:"done"`
	Error   string        `json:"error,omitempty"`
}

// Message represents a chat message
type Message struct {
	Role      string     `json:"role"`
	Content   string     `json:"content"`
	ToolCalls []ToolCall `json:"tool_calls,omitempty"`
}

// Choice represents a response choice
type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
}

// StreamChoice represents a streaming response choice
type StreamChoice struct {
	Index int           `json:"index"`
	Delta MessageDelta  `json:"delta"`
	FinishReason string `json:"finish_reason"`
}

// MessageDelta represents a message delta in streaming
type MessageDelta struct {
	Role      string     `json:"role,omitempty"`
	Content   string     `json:"content,omitempty"`
	ToolCalls []ToolCall `json:"tool_calls,omitempty"`
}

// Tool represents a function tool
type Tool struct {
	Type     string       `json:"type"`
	Function ToolFunction `json:"function"`
}

// ToolFunction represents a tool function definition
type ToolFunction struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// ToolCall represents a tool call
type ToolCall struct {
	ID       string           `json:"id"`
	Type     string           `json:"type"`
	Function ToolCallFunction `json:"function"`
}

// ToolCallFunction represents a tool call function
type ToolCallFunction struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// Usage represents token usage
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// Model represents an AI model
type Model struct {
	ID          string   `json:"id"`
	Object      string   `json:"object"`
	Created     int64    `json:"created"`
	OwnedBy     string   `json:"owned_by"`
	Capabilities []string `json:"capabilities,omitempty"`
}

// ToolResult represents the result of a tool execution
type ToolResult struct {
	ToolCallID string      `json:"tool_call_id"`
	Content    string      `json:"content"`
	Success    bool        `json:"success"`
	Error      string      `json:"error,omitempty"`
	Data       interface{} `json:"data,omitempty"`
}

// NewErrorToolResult creates a new error tool result
func NewErrorToolResult(toolCallID string, err error) *ToolResult {
	return &ToolResult{
		ToolCallID: toolCallID,
		Content:    fmt.Sprintf("Error: %v", err),
		Success:    false,
		Error:      err.Error(),
	}
}

// NewSuccessToolResult creates a new success tool result
func NewSuccessToolResult(toolCallID, content string, data interface{}) *ToolResult {
	return &ToolResult{
		ToolCallID: toolCallID,
		Content:    content,
		Success:    true,
		Data:       data,
	}
}

// FormatToolResult formats a tool result for display
func FormatToolResult(result *ToolResult) string {
	if result.Success {
		return result.Content
	}
	return fmt.Sprintf("Error: %s", result.Error)
}

// ParseToolArguments parses tool call arguments from JSON string
func ParseToolArguments(arguments string, target interface{}) error {
	return json.Unmarshal([]byte(arguments), target)
}

// FormatToolResult formats a tool result as JSON
func FormatToolResult(result *ToolResult) string {
	data, _ := json.Marshal(result)
	return string(data)
}

// NewToolResult creates a new tool result
func NewToolResult(toolCallID string, success bool, content string, data interface{}) *ToolResult {
	return &ToolResult{
		ToolCallID: toolCallID,
		Success:    success,
		Content:    content,
		Data:       data,
	}
}

// NewErrorToolResult creates a new error tool result
func NewErrorToolResult(toolCallID string, err error) *ToolResult {
	return &ToolResult{
		ToolCallID: toolCallID,
		Success:    false,
		Error:      err.Error(),
		Content:    "Tool execution failed: " + err.Error(),
	}
}
