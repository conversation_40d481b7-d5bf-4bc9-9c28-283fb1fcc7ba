package tools

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/arien-ai/cli/internal/ai/providers"
)

// Tool represents a function tool that can be called by the AI
type Tool interface {
	// Name returns the tool name
	Name() string
	
	// Description returns the tool description
	Description() string
	
	// Parameters returns the tool parameters schema
	Parameters() map[string]interface{}
	
	// Execute executes the tool with given arguments
	Execute(ctx context.Context, args map[string]interface{}) (*providers.ToolResult, error)
	
	// IsEnabled returns whether the tool is enabled
	IsEnabled() bool
	
	// SetEnabled sets the tool enabled state
	SetEnabled(enabled bool)
}

// Registry manages all available tools
type Registry struct {
	tools map[string]Tool
}

// NewRegistry creates a new tool registry
func NewRegistry() *Registry {
	registry := &Registry{
		tools: make(map[string]Tool),
	}
	
	// Register all available tools
	registry.Register(NewBashTool())
	registry.Register(NewGrepTool())
	registry.Register(NewGlobTool())
	registry.Register(NewWriteTool())
	registry.Register(NewEditTool())
	registry.Register(NewWebTool())
	
	return registry
}

// Register registers a tool
func (r *Registry) Register(tool Tool) {
	r.tools[tool.Name()] = tool
}

// Get gets a tool by name
func (r *Registry) Get(name string) (Tool, bool) {
	tool, exists := r.tools[name]
	return tool, exists
}

// GetAll returns all registered tools
func (r *Registry) GetAll() map[string]Tool {
	return r.tools
}

// GetEnabled returns all enabled tools
func (r *Registry) GetEnabled() map[string]Tool {
	enabled := make(map[string]Tool)
	for name, tool := range r.tools {
		if tool.IsEnabled() {
			enabled[name] = tool
		}
	}
	return enabled
}

// Execute executes a tool by name with given arguments
func (r *Registry) Execute(ctx context.Context, name string, args map[string]interface{}) (*providers.ToolResult, error) {
	tool, exists := r.tools[name]
	if !exists {
		return providers.NewErrorToolResult("", fmt.Errorf("tool not found: %s", name)), nil
	}
	
	if !tool.IsEnabled() {
		return providers.NewErrorToolResult("", fmt.Errorf("tool disabled: %s", name)), nil
	}
	
	return tool.Execute(ctx, args)
}

// ToProviderTools converts registry tools to provider tool format
func (r *Registry) ToProviderTools() []providers.Tool {
	var providerTools []providers.Tool
	
	for _, tool := range r.GetEnabled() {
		providerTools = append(providerTools, providers.Tool{
			Type: "function",
			Function: providers.ToolFunction{
				Name:        tool.Name(),
				Description: tool.Description(),
				Parameters:  tool.Parameters(),
			},
		})
	}
	
	return providerTools
}

// ExecuteToolCall executes a tool call from the AI provider
func (r *Registry) ExecuteToolCall(ctx context.Context, toolCall providers.ToolCall) (*providers.ToolResult, error) {
	// Parse arguments
	var args map[string]interface{}
	if err := json.Unmarshal([]byte(toolCall.Function.Arguments), &args); err != nil {
		return providers.NewErrorToolResult(toolCall.ID, fmt.Errorf("invalid tool arguments: %w", err)), nil
	}
	
	// Execute tool
	result, err := r.Execute(ctx, toolCall.Function.Name, args)
	if err != nil {
		return providers.NewErrorToolResult(toolCall.ID, err), nil
	}
	
	// Set tool call ID
	result.ToolCallID = toolCall.ID
	
	return result, nil
}

// BaseTool provides common functionality for tools
type BaseTool struct {
	name        string
	description string
	parameters  map[string]interface{}
	enabled     bool
}

// NewBaseTool creates a new base tool
func NewBaseTool(name, description string, parameters map[string]interface{}) *BaseTool {
	return &BaseTool{
		name:        name,
		description: description,
		parameters:  parameters,
		enabled:     true,
	}
}

// Name returns the tool name
func (t *BaseTool) Name() string {
	return t.name
}

// Description returns the tool description
func (t *BaseTool) Description() string {
	return t.description
}

// Parameters returns the tool parameters schema
func (t *BaseTool) Parameters() map[string]interface{} {
	return t.parameters
}

// IsEnabled returns whether the tool is enabled
func (t *BaseTool) IsEnabled() bool {
	return t.enabled
}

// SetEnabled sets the tool enabled state
func (t *BaseTool) SetEnabled(enabled bool) {
	t.enabled = enabled
}

// ValidateArgs validates tool arguments against the schema
func (t *BaseTool) ValidateArgs(args map[string]interface{}) error {
	// Basic validation - check required fields
	if properties, ok := t.parameters["properties"].(map[string]interface{}); ok {
		if required, ok := t.parameters["required"].([]interface{}); ok {
			for _, req := range required {
				if reqStr, ok := req.(string); ok {
					if _, exists := args[reqStr]; !exists {
						return fmt.Errorf("missing required parameter: %s", reqStr)
					}
				}
			}
		}
		
		// Check parameter types
		for key, value := range args {
			if prop, exists := properties[key]; exists {
				if propMap, ok := prop.(map[string]interface{}); ok {
					if expectedType, ok := propMap["type"].(string); ok {
						if !validateType(value, expectedType) {
							return fmt.Errorf("invalid type for parameter %s: expected %s", key, expectedType)
						}
					}
				}
			}
		}
	}
	
	return nil
}

// validateType validates a value against an expected type
func validateType(value interface{}, expectedType string) bool {
	switch expectedType {
	case "string":
		_, ok := value.(string)
		return ok
	case "number":
		switch value.(type) {
		case int, int32, int64, float32, float64:
			return true
		}
		return false
	case "boolean":
		_, ok := value.(bool)
		return ok
	case "array":
		_, ok := value.([]interface{})
		return ok
	case "object":
		_, ok := value.(map[string]interface{})
		return ok
	default:
		return true // Unknown type, allow it
	}
}
