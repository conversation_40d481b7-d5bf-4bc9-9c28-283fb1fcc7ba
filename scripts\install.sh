#!/bin/bash

# Arien AI CLI Universal Installer Script
# Supports Windows 11 WSL, macOS, and Linux
# Usage: curl -sSL https://raw.githubusercontent.com/arien-ai/cli/main/scripts/install.sh | bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
REPO_OWNER="arien-ai"
REPO_NAME="cli"
BINARY_NAME="arien"
INSTALL_DIR="/usr/local/bin"
CONFIG_DIR="$HOME/.config/arien-ai"
VERSION="latest"
FORCE_INSTALL=false
UNINSTALL=false
UPDATE=false

# Platform detection
OS=""
ARCH=""
PLATFORM=""

# Functions
print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                        ARIEN AI CLI INSTALLER                               ║"
    echo "║                   Universal Installation Script                             ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

detect_platform() {
    # Detect OS
    case "$(uname -s)" in
        Linux*)
            OS="linux"
            if grep -q Microsoft /proc/version 2>/dev/null; then
                log_info "Detected Windows Subsystem for Linux (WSL)"
                PLATFORM="wsl"
            else
                PLATFORM="linux"
            fi
            ;;
        Darwin*)
            OS="darwin"
            PLATFORM="macos"
            ;;
        CYGWIN*|MINGW*|MSYS*)
            OS="windows"
            PLATFORM="windows"
            ;;
        *)
            log_error "Unsupported operating system: $(uname -s)"
            exit 1
            ;;
    esac

    # Detect architecture
    case "$(uname -m)" in
        x86_64|amd64)
            ARCH="amd64"
            ;;
        arm64|aarch64)
            ARCH="arm64"
            ;;
        armv7l)
            ARCH="arm"
            ;;
        i386|i686)
            ARCH="386"
            ;;
        *)
            log_error "Unsupported architecture: $(uname -m)"
            exit 1
            ;;
    esac

    log_info "Detected platform: $PLATFORM ($OS/$ARCH)"
}

check_dependencies() {
    local deps=("curl" "tar")
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "Required dependency '$dep' is not installed"
            exit 1
        fi
    done
    
    log_success "All dependencies are available"
}

get_latest_version() {
    log_info "Fetching latest version information..."
    
    local api_url="https://api.github.com/repos/$REPO_OWNER/$REPO_NAME/releases/latest"
    VERSION=$(curl -s "$api_url" | grep '"tag_name":' | sed -E 's/.*"([^"]+)".*/\1/')
    
    if [ -z "$VERSION" ]; then
        log_warning "Could not fetch latest version, using 'latest'"
        VERSION="latest"
    else
        log_info "Latest version: $VERSION"
    fi
}

download_binary() {
    local download_url
    local temp_dir
    local archive_name
    
    if [ "$VERSION" = "latest" ]; then
        download_url="https://github.com/$REPO_OWNER/$REPO_NAME/releases/latest/download"
    else
        download_url="https://github.com/$REPO_OWNER/$REPO_NAME/releases/download/$VERSION"
    fi
    
    archive_name="${BINARY_NAME}_${OS}_${ARCH}.tar.gz"
    download_url="$download_url/$archive_name"
    
    temp_dir=$(mktemp -d)
    local archive_path="$temp_dir/$archive_name"
    
    log_info "Downloading $BINARY_NAME from $download_url"
    
    if ! curl -L -o "$archive_path" "$download_url"; then
        log_error "Failed to download binary"
        rm -rf "$temp_dir"
        exit 1
    fi
    
    log_info "Extracting binary..."
    tar -xzf "$archive_path" -C "$temp_dir"
    
    # Find the binary in the extracted files
    local binary_path="$temp_dir/$BINARY_NAME"
    if [ ! -f "$binary_path" ]; then
        # Try alternative paths
        binary_path=$(find "$temp_dir" -name "$BINARY_NAME" -type f | head -1)
        if [ -z "$binary_path" ]; then
            log_error "Binary not found in downloaded archive"
            rm -rf "$temp_dir"
            exit 1
        fi
    fi
    
    # Make binary executable
    chmod +x "$binary_path"
    
    echo "$binary_path"
}

install_binary() {
    local binary_path="$1"
    local install_path="$INSTALL_DIR/$BINARY_NAME"
    
    log_info "Installing binary to $install_path"
    
    # Check if we need sudo
    if [ ! -w "$INSTALL_DIR" ]; then
        if command -v sudo &> /dev/null; then
            sudo cp "$binary_path" "$install_path"
            sudo chmod +x "$install_path"
        else
            log_error "Cannot write to $INSTALL_DIR and sudo is not available"
            log_info "Please run as root or install to a writable directory"
            exit 1
        fi
    else
        cp "$binary_path" "$install_path"
        chmod +x "$install_path"
    fi
    
    log_success "Binary installed successfully"
}

create_config_directory() {
    if [ ! -d "$CONFIG_DIR" ]; then
        log_info "Creating configuration directory: $CONFIG_DIR"
        mkdir -p "$CONFIG_DIR"
    fi
}

setup_shell_completion() {
    local shell_name
    shell_name=$(basename "$SHELL")
    
    case "$shell_name" in
        bash)
            setup_bash_completion
            ;;
        zsh)
            setup_zsh_completion
            ;;
        fish)
            setup_fish_completion
            ;;
        *)
            log_warning "Shell completion not supported for $shell_name"
            ;;
    esac
}

setup_bash_completion() {
    local completion_dir="$HOME/.bash_completion.d"
    local completion_file="$completion_dir/arien"
    
    mkdir -p "$completion_dir"
    
    if command -v "$BINARY_NAME" &> /dev/null; then
        "$BINARY_NAME" completion bash > "$completion_file" 2>/dev/null || true
        log_info "Bash completion installed to $completion_file"
    fi
}

setup_zsh_completion() {
    local completion_dir="$HOME/.zsh/completions"
    local completion_file="$completion_dir/_arien"
    
    mkdir -p "$completion_dir"
    
    if command -v "$BINARY_NAME" &> /dev/null; then
        "$BINARY_NAME" completion zsh > "$completion_file" 2>/dev/null || true
        log_info "Zsh completion installed to $completion_file"
    fi
}

setup_fish_completion() {
    local completion_dir="$HOME/.config/fish/completions"
    local completion_file="$completion_dir/arien.fish"
    
    mkdir -p "$completion_dir"
    
    if command -v "$BINARY_NAME" &> /dev/null; then
        "$BINARY_NAME" completion fish > "$completion_file" 2>/dev/null || true
        log_info "Fish completion installed to $completion_file"
    fi
}

verify_installation() {
    if command -v "$BINARY_NAME" &> /dev/null; then
        local installed_version
        installed_version=$("$BINARY_NAME" version --short 2>/dev/null || echo "unknown")
        log_success "Installation verified! Version: $installed_version"
        return 0
    else
        log_error "Installation verification failed"
        return 1
    fi
}

uninstall_arien() {
    log_info "Uninstalling Arien AI CLI..."
    
    # Remove binary
    local install_path="$INSTALL_DIR/$BINARY_NAME"
    if [ -f "$install_path" ]; then
        if [ -w "$INSTALL_DIR" ]; then
            rm -f "$install_path"
        else
            sudo rm -f "$install_path"
        fi
        log_success "Binary removed from $install_path"
    fi
    
    # Remove completions
    rm -f "$HOME/.bash_completion.d/arien"
    rm -f "$HOME/.zsh/completions/_arien"
    rm -f "$HOME/.config/fish/completions/arien.fish"
    
    # Ask about config directory
    echo -n "Remove configuration directory $CONFIG_DIR? [y/N]: "
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        rm -rf "$CONFIG_DIR"
        log_success "Configuration directory removed"
    fi
    
    log_success "Arien AI CLI uninstalled successfully"
}

update_arien() {
    log_info "Updating Arien AI CLI..."
    
    # Check current version
    if command -v "$BINARY_NAME" &> /dev/null; then
        local current_version
        current_version=$("$BINARY_NAME" version --short 2>/dev/null || echo "unknown")
        log_info "Current version: $current_version"
    fi
    
    # Proceed with installation (which will overwrite)
    FORCE_INSTALL=true
    main_install
}

main_install() {
    print_banner
    
    detect_platform
    check_dependencies
    
    if [ "$VERSION" = "latest" ]; then
        get_latest_version
    fi
    
    # Check if already installed
    if command -v "$BINARY_NAME" &> /dev/null && [ "$FORCE_INSTALL" = false ]; then
        log_warning "Arien AI CLI is already installed"
        echo -n "Do you want to reinstall? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "Installation cancelled"
            exit 0
        fi
    fi
    
    # Download and install
    local binary_path
    binary_path=$(download_binary)
    
    create_config_directory
    install_binary "$binary_path"
    
    # Clean up
    rm -rf "$(dirname "$binary_path")"
    
    # Setup shell completion
    setup_shell_completion
    
    # Verify installation
    if verify_installation; then
        echo
        log_success "Arien AI CLI installed successfully!"
        echo
        log_info "Next steps:"
        echo "  1. Run 'arien setup' to configure your AI providers"
        echo "  2. Run 'arien --help' to see available commands"
        echo "  3. Start using: 'arien'"
        echo
        log_info "Configuration directory: $CONFIG_DIR"
    else
        log_error "Installation failed"
        exit 1
    fi
}

show_help() {
    echo "Arien AI CLI Universal Installer"
    echo
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  -h, --help      Show this help message"
    echo "  -v, --version   Install specific version"
    echo "  -f, --force     Force installation even if already installed"
    echo "  -u, --uninstall Uninstall Arien AI CLI"
    echo "  --update        Update to latest version"
    echo
    echo "Examples:"
    echo "  $0                    # Install latest version"
    echo "  $0 -v v1.0.0         # Install specific version"
    echo "  $0 --update          # Update to latest version"
    echo "  $0 --uninstall       # Uninstall"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -f|--force)
            FORCE_INSTALL=true
            shift
            ;;
        -u|--uninstall)
            UNINSTALL=true
            shift
            ;;
        --update)
            UPDATE=true
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
if [ "$UNINSTALL" = true ]; then
    uninstall_arien
elif [ "$UPDATE" = true ]; then
    update_arien
else
    main_install
fi
