#!/usr/bin/env pwsh
# Arien AI CLI Installation Script for Windows, macOS, and Linux
# This script provides options to install, update, or uninstall the CLI system globally

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("install", "update", "uninstall", "status")]
    [string]$Action = "install",
    
    [Parameter(Mandatory=$false)]
    [string]$Version = "latest",
    
    [Parameter(Mandatory=$false)]
    [switch]$Force = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Local = $false
)

# Configuration
$REPO_URL = "https://github.com/arien-ai/cli"
$BINARY_NAME = "arien"
$CONFIG_DIR_NAME = "arien-ai"

# Platform detection
$OS = ""
$ARCH = ""

if ($IsWindows -or $env:OS -eq "Windows_NT") {
    $OS = "windows"
    $BINARY_NAME += ".exe"
} elseif ($IsLinux) {
    $OS = "linux"
} elseif ($IsMacOS) {
    $OS = "darwin"
} else {
    Write-Error "Unsupported operating system"
    exit 1
}

# Architecture detection
$ARCH_INFO = uname -m 2>$null
if (-not $ARCH_INFO) {
    $ARCH_INFO = $env:PROCESSOR_ARCHITECTURE
}

switch -Regex ($ARCH_INFO) {
    "x86_64|amd64" { $ARCH = "amd64" }
    "arm64|aarch64" { $ARCH = "arm64" }
    "armv7l" { $ARCH = "arm" }
    default { 
        Write-Error "Unsupported architecture: $ARCH_INFO"
        exit 1
    }
}

# Paths
if ($Local) {
    $INSTALL_DIR = Join-Path $HOME ".local/bin"
    $CONFIG_DIR = Join-Path $HOME ".config/$CONFIG_DIR_NAME"
} else {
    if ($OS -eq "windows") {
        $INSTALL_DIR = "$env:ProgramFiles\ArienAI"
        $CONFIG_DIR = Join-Path $env:APPDATA $CONFIG_DIR_NAME
    } else {
        $INSTALL_DIR = "/usr/local/bin"
        $CONFIG_DIR = Join-Path $HOME ".config/$CONFIG_DIR_NAME"
    }
}

$BINARY_PATH = Join-Path $INSTALL_DIR $BINARY_NAME
$CONFIG_FILE = Join-Path $CONFIG_DIR "arien-ai.yaml"

# Colors for output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Red" = [ConsoleColor]::Red
        "Green" = [ConsoleColor]::Green
        "Yellow" = [ConsoleColor]::Yellow
        "Blue" = [ConsoleColor]::Blue
        "Cyan" = [ConsoleColor]::Cyan
        "White" = [ConsoleColor]::White
    }
    
    Write-Host $Message -ForegroundColor $colorMap[$Color]
}

function Write-Success { param([string]$Message) Write-ColorOutput "✓ $Message" "Green" }
function Write-Error { param([string]$Message) Write-ColorOutput "✗ $Message" "Red" }
function Write-Warning { param([string]$Message) Write-ColorOutput "⚠ $Message" "Yellow" }
function Write-Info { param([string]$Message) Write-ColorOutput "ℹ $Message" "Blue" }

# Helper functions
function Test-AdminRights {
    if ($OS -eq "windows") {
        $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
        $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
        return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    } else {
        return (id -u) -eq 0
    }
}

function Get-LatestVersion {
    try {
        $response = Invoke-RestMethod -Uri "$REPO_URL/releases/latest" -Headers @{"User-Agent"="ArienAI-Installer"}
        return $response.tag_name -replace '^v', ''
    } catch {
        Write-Warning "Could not fetch latest version, using 'latest'"
        return "latest"
    }
}

function Get-DownloadUrl {
    param([string]$Version)
    
    if ($Version -eq "latest") {
        $Version = Get-LatestVersion
    }
    
    $filename = "arien-ai_${Version}_${OS}_${ARCH}"
    if ($OS -eq "windows") {
        $filename += ".zip"
    } else {
        $filename += ".tar.gz"
    }
    
    return "$REPO_URL/releases/download/v$Version/$filename"
}

function Install-ArienAI {
    Write-Info "Installing Arien AI CLI..."
    Write-Info "OS: $OS, Architecture: $ARCH"
    Write-Info "Install Directory: $INSTALL_DIR"
    
    # Check admin rights for global installation
    if (-not $Local -and -not (Test-AdminRights)) {
        Write-Error "Global installation requires administrator privileges"
        Write-Info "Run with -Local flag for user installation, or run as administrator"
        exit 1
    }
    
    # Create install directory
    if (-not (Test-Path $INSTALL_DIR)) {
        try {
            New-Item -ItemType Directory -Path $INSTALL_DIR -Force | Out-Null
            Write-Success "Created install directory: $INSTALL_DIR"
        } catch {
            Write-Error "Failed to create install directory: $_"
            exit 1
        }
    }
    
    # Download and install
    $downloadUrl = Get-DownloadUrl $Version
    $tempFile = Join-Path $env:TEMP "arien-ai-download"
    
    Write-Info "Downloading from: $downloadUrl"
    
    try {
        Invoke-WebRequest -Uri $downloadUrl -OutFile $tempFile -UserAgent "ArienAI-Installer"
        Write-Success "Downloaded successfully"
    } catch {
        Write-Error "Failed to download: $_"
        exit 1
    }
    
    # Extract and install
    try {
        if ($OS -eq "windows") {
            Expand-Archive -Path $tempFile -DestinationPath $INSTALL_DIR -Force
        } else {
            tar -xzf $tempFile -C $INSTALL_DIR
        }
        
        # Make executable on Unix systems
        if ($OS -ne "windows") {
            chmod +x $BINARY_PATH
        }
        
        Write-Success "Installed binary to: $BINARY_PATH"
    } catch {
        Write-Error "Failed to extract/install: $_"
        exit 1
    } finally {
        Remove-Item $tempFile -ErrorAction SilentlyContinue
    }
    
    # Create config directory
    if (-not (Test-Path $CONFIG_DIR)) {
        New-Item -ItemType Directory -Path $CONFIG_DIR -Force | Out-Null
        Write-Success "Created config directory: $CONFIG_DIR"
    }
    
    # Initialize configuration if it doesn't exist
    if (-not (Test-Path $CONFIG_FILE)) {
        & $BINARY_PATH config init
        Write-Success "Initialized configuration"
    }
    
    # Update PATH if needed
    Update-Path
    
    Write-Success "Arien AI CLI installed successfully!"
    Write-Info "Run 'arien setup' to configure your AI providers"
}

function Update-ArienAI {
    Write-Info "Updating Arien AI CLI..."
    
    if (-not (Test-Path $BINARY_PATH)) {
        Write-Error "Arien AI CLI is not installed"
        Write-Info "Use 'install' action to install it first"
        exit 1
    }
    
    # Get current version
    $currentVersion = & $BINARY_PATH version --short 2>$null
    $latestVersion = Get-LatestVersion
    
    if ($currentVersion -eq $latestVersion -and -not $Force) {
        Write-Success "Already up to date (version $currentVersion)"
        return
    }
    
    Write-Info "Updating from $currentVersion to $latestVersion"
    Install-ArienAI
}

function Uninstall-ArienAI {
    Write-Info "Uninstalling Arien AI CLI..."
    
    # Remove binary
    if (Test-Path $BINARY_PATH) {
        Remove-Item $BINARY_PATH -Force
        Write-Success "Removed binary: $BINARY_PATH"
    }
    
    # Ask about config removal
    $removeConfig = Read-Host "Remove configuration directory? (y/N)"
    if ($removeConfig -eq "y" -or $removeConfig -eq "Y") {
        if (Test-Path $CONFIG_DIR) {
            Remove-Item $CONFIG_DIR -Recurse -Force
            Write-Success "Removed configuration: $CONFIG_DIR"
        }
    }
    
    Write-Success "Arien AI CLI uninstalled successfully!"
}

function Show-Status {
    Write-Info "Arien AI CLI Status"
    Write-Host "===================="
    
    if (Test-Path $BINARY_PATH) {
        $version = & $BINARY_PATH version --short 2>$null
        Write-Success "Installed: Yes (version $version)"
        Write-Info "Binary: $BINARY_PATH"
    } else {
        Write-Error "Installed: No"
    }
    
    if (Test-Path $CONFIG_FILE) {
        Write-Success "Configured: Yes"
        Write-Info "Config: $CONFIG_FILE"
    } else {
        Write-Warning "Configured: No"
    }
    
    Write-Info "Install Directory: $INSTALL_DIR"
    Write-Info "Config Directory: $CONFIG_DIR"
}

function Update-Path {
    if ($OS -eq "windows") {
        # Windows PATH update
        $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
        if ($currentPath -notlike "*$INSTALL_DIR*") {
            [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$INSTALL_DIR", "User")
            Write-Success "Added to PATH (restart terminal to take effect)"
        }
    } else {
        # Unix PATH update
        $shellProfile = ""
        if (Test-Path "$HOME/.bashrc") { $shellProfile = "$HOME/.bashrc" }
        elseif (Test-Path "$HOME/.zshrc") { $shellProfile = "$HOME/.zshrc" }
        elseif (Test-Path "$HOME/.profile") { $shellProfile = "$HOME/.profile" }
        
        if ($shellProfile -and $Local) {
            $pathLine = "export PATH=`"$INSTALL_DIR:`$PATH`""
            $content = Get-Content $shellProfile -ErrorAction SilentlyContinue
            if ($content -notcontains $pathLine) {
                Add-Content $shellProfile $pathLine
                Write-Success "Added to PATH in $shellProfile (restart terminal to take effect)"
            }
        }
    }
}

# Main execution
Write-ColorOutput "🚀 Arien AI CLI Installer" "Cyan"
Write-Host "=========================="

switch ($Action) {
    "install" { Install-ArienAI }
    "update" { Update-ArienAI }
    "uninstall" { Uninstall-ArienAI }
    "status" { Show-Status }
    default {
        Write-Error "Unknown action: $Action"
        Write-Info "Available actions: install, update, uninstall, status"
        exit 1
    }
}
