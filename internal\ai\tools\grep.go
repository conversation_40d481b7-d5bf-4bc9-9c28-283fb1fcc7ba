package tools

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/arien-ai/cli/internal/ai/providers"
)

// GrepTool implements content search functionality
type GrepTool struct {
	*BaseTool
}

// NewGrepTool creates a new grep tool
func NewGrepTool() *GrepTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"pattern": map[string]interface{}{
				"type":        "string",
				"description": "The search pattern (supports regex). Use proper regex syntax for complex patterns.",
			},
			"path": map[string]interface{}{
				"type":        "string",
				"description": "File or directory path to search in. Defaults to current directory.",
			},
			"recursive": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether to search recursively in subdirectories. Defaults to true.",
			},
			"case_sensitive": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether the search should be case sensitive. Defaults to false.",
			},
			"file_pattern": map[string]interface{}{
				"type":        "string",
				"description": "File name pattern to filter files (e.g., '*.go', '*.txt'). Defaults to all files.",
			},
			"max_results": map[string]interface{}{
				"type":        "number",
				"description": "Maximum number of matching files to return. Defaults to 50.",
			},
			"context_lines": map[string]interface{}{
				"type":        "number",
				"description": "Number of context lines to show around matches. Defaults to 2.",
			},
			"exclude_dirs": map[string]interface{}{
				"type":        "array",
				"description": "Directories to exclude from search (e.g., ['.git', 'node_modules']).",
				"items": map[string]interface{}{
					"type": "string",
				},
			},
		},
		"required": []string{"pattern"},
	}

	description := `Fast content search tool that finds files containing specific text or patterns.

USAGE GUIDELINES:
- Searches for text patterns within files using regular expressions
- Returns matching file paths sorted by modification time (newest first)
- Supports recursive directory traversal and file filtering
- Provides context lines around matches for better understanding
- Excludes common directories like .git, node_modules by default

WHEN TO USE:
- Finding files containing specific text or code patterns
- Searching for function definitions, variable names, or comments
- Locating configuration values or error messages
- Code analysis and debugging tasks
- Documentation searches

WHEN NOT TO USE:
- For finding files by name (use glob tool instead)
- For simple file listing (use bash tool with ls)
- For binary file searches (limited text support)
- For very large codebases (may be slow)

SEARCH PATTERNS:
- Simple text: "function main"
- Regex patterns: "func\s+\w+\("
- Case insensitive: set case_sensitive to false
- Word boundaries: "\bword\b"

EXAMPLES:
- Find function definitions: {"pattern": "func\\s+\\w+\\(", "file_pattern": "*.go"}
- Search for TODO comments: {"pattern": "TODO|FIXME", "case_sensitive": false}
- Find imports: {"pattern": "import\\s+", "path": "./src", "recursive": true}
- Search specific files: {"pattern": "error", "file_pattern": "*.log"}

PERFORMANCE TIPS:
- Use specific file patterns to limit search scope
- Exclude unnecessary directories with exclude_dirs
- Set reasonable max_results to avoid overwhelming output
- Use more specific patterns to reduce false positives`

	return &GrepTool{
		BaseTool: NewBaseTool("grep", description, parameters),
	}
}

// Execute executes the grep search
func (t *GrepTool) Execute(ctx context.Context, args map[string]interface{}) (*providers.ToolResult, error) {
	// Validate arguments
	if err := t.ValidateArgs(args); err != nil {
		return providers.NewErrorToolResult("", err), nil
	}

	pattern, ok := args["pattern"].(string)
	if !ok {
		return providers.NewErrorToolResult("", fmt.Errorf("pattern must be a string")), nil
	}

	// Get optional parameters
	searchPath := "."
	if p, ok := args["path"].(string); ok {
		searchPath = p
	}

	recursive := true
	if r, ok := args["recursive"].(bool); ok {
		recursive = r
	}

	caseSensitive := false
	if cs, ok := args["case_sensitive"].(bool); ok {
		caseSensitive = cs
	}

	filePattern := "*"
	if fp, ok := args["file_pattern"].(string); ok {
		filePattern = fp
	}

	maxResults := 50
	if mr, ok := args["max_results"].(float64); ok {
		maxResults = int(mr)
	}

	contextLines := 2
	if cl, ok := args["context_lines"].(float64); ok {
		contextLines = int(cl)
	}

	excludeDirs := []string{".git", "node_modules", ".vscode", ".idea", "vendor", "target", "build", "dist"}
	if ed, ok := args["exclude_dirs"].([]interface{}); ok {
		excludeDirs = make([]string, len(ed))
		for i, dir := range ed {
			if dirStr, ok := dir.(string); ok {
				excludeDirs[i] = dirStr
			}
		}
	}

	// Compile regex pattern
	var regex *regexp.Regexp
	var err error
	if caseSensitive {
		regex, err = regexp.Compile(pattern)
	} else {
		regex, err = regexp.Compile("(?i)" + pattern)
	}
	if err != nil {
		return providers.NewErrorToolResult("", fmt.Errorf("invalid regex pattern: %w", err)), nil
	}

	// Perform search
	matches, err := t.searchFiles(ctx, searchPath, regex, filePattern, recursive, contextLines, excludeDirs, maxResults)
	if err != nil {
		return providers.NewErrorToolResult("", err), nil
	}

	// Sort by modification time (newest first)
	sort.Slice(matches, func(i, j int) bool {
		return matches[i].ModTime.After(matches[j].ModTime)
	})

	// Prepare result
	result := &providers.ToolResult{
		Success: true,
		Data: map[string]interface{}{
			"pattern":       pattern,
			"path":          searchPath,
			"matches_found": len(matches),
			"matches":       matches,
			"search_params": map[string]interface{}{
				"recursive":      recursive,
				"case_sensitive": caseSensitive,
				"file_pattern":   filePattern,
				"max_results":    maxResults,
				"context_lines":  contextLines,
				"exclude_dirs":   excludeDirs,
			},
		},
	}

	if len(matches) == 0 {
		result.Content = fmt.Sprintf("No matches found for pattern '%s' in path '%s'", pattern, searchPath)
	} else {
		result.Content = fmt.Sprintf("Found %d files containing pattern '%s'", len(matches), pattern)
	}

	return result, nil
}

// FileMatch represents a file with matching content
type FileMatch struct {
	Path     string      `json:"path"`
	ModTime  time.Time   `json:"mod_time"`
	Size     int64       `json:"size"`
	Matches  []LineMatch `json:"matches"`
	Preview  string      `json:"preview"`
}

// LineMatch represents a matching line
type LineMatch struct {
	LineNumber int      `json:"line_number"`
	Content    string   `json:"content"`
	Context    []string `json:"context,omitempty"`
}

// searchFiles performs the actual file search
func (t *GrepTool) searchFiles(ctx context.Context, searchPath string, regex *regexp.Regexp, filePattern string, recursive bool, contextLines int, excludeDirs []string, maxResults int) ([]FileMatch, error) {
	var matches []FileMatch
	
	err := filepath.Walk(searchPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // Skip files with errors
		}

		// Check context cancellation
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// Skip directories if not recursive
		if info.IsDir() {
			if !recursive && path != searchPath {
				return filepath.SkipDir
			}
			
			// Skip excluded directories
			for _, excludeDir := range excludeDirs {
				if strings.Contains(path, excludeDir) {
					return filepath.SkipDir
				}
			}
			return nil
		}

		// Check file pattern
		if matched, _ := filepath.Match(filePattern, info.Name()); !matched && filePattern != "*" {
			return nil
		}

		// Skip binary files (basic check)
		if t.isBinaryFile(path) {
			return nil
		}

		// Search in file
		fileMatches, err := t.searchInFile(path, regex, contextLines)
		if err != nil {
			return nil // Skip files with errors
		}

		if len(fileMatches) > 0 {
			match := FileMatch{
				Path:    path,
				ModTime: info.ModTime(),
				Size:    info.Size(),
				Matches: fileMatches,
				Preview: t.generatePreview(fileMatches),
			}
			matches = append(matches, match)

			// Check max results
			if len(matches) >= maxResults {
				return fmt.Errorf("max results reached")
			}
		}

		return nil
	})

	if err != nil && err.Error() != "max results reached" {
		return nil, err
	}

	return matches, nil
}

// searchInFile searches for pattern in a single file
func (t *GrepTool) searchInFile(filePath string, regex *regexp.Regexp, contextLines int) ([]LineMatch, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var matches []LineMatch
	var lines []string
	
	scanner := bufio.NewScanner(file)
	lineNumber := 0
	
	// Read all lines first for context
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}
	
	if err := scanner.Err(); err != nil {
		return nil, err
	}

	// Search for matches
	for i, line := range lines {
		lineNumber = i + 1
		if regex.MatchString(line) {
			match := LineMatch{
				LineNumber: lineNumber,
				Content:    line,
			}
			
			// Add context lines
			if contextLines > 0 {
				start := max(0, i-contextLines)
				end := min(len(lines), i+contextLines+1)
				
				for j := start; j < end; j++ {
					if j != i {
						match.Context = append(match.Context, fmt.Sprintf("%d: %s", j+1, lines[j]))
					}
				}
			}
			
			matches = append(matches, match)
		}
	}

	return matches, nil
}

// isBinaryFile checks if a file is likely binary
func (t *GrepTool) isBinaryFile(filePath string) bool {
	file, err := os.Open(filePath)
	if err != nil {
		return true
	}
	defer file.Close()

	// Read first 512 bytes
	buffer := make([]byte, 512)
	n, err := file.Read(buffer)
	if err != nil {
		return true
	}

	// Check for null bytes (common in binary files)
	for i := 0; i < n; i++ {
		if buffer[i] == 0 {
			return true
		}
	}

	return false
}

// generatePreview generates a preview of the matches
func (t *GrepTool) generatePreview(matches []LineMatch) string {
	if len(matches) == 0 {
		return ""
	}

	var preview strings.Builder
	maxPreviewMatches := min(3, len(matches))
	
	for i := 0; i < maxPreviewMatches; i++ {
		match := matches[i]
		preview.WriteString(fmt.Sprintf("Line %d: %s", match.LineNumber, match.Content))
		if i < maxPreviewMatches-1 {
			preview.WriteString("\n")
		}
	}
	
	if len(matches) > maxPreviewMatches {
		preview.WriteString(fmt.Sprintf("\n... and %d more matches", len(matches)-maxPreviewMatches))
	}

	return preview.String()
}

// Helper functions
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
