package providers

import (
	"context"
	"fmt"
	"strings"

	"github.com/arien-ai/cli/internal/config"
)

// ProviderFactory creates AI providers based on configuration
type ProviderFactory struct {
	config *config.Config
}

// NewProviderFactory creates a new provider factory
func NewProviderFactory(cfg *config.Config) *ProviderFactory {
	return &ProviderFactory{
		config: cfg,
	}
}

// CreateProvider creates a provider based on the configuration
func (f *ProviderFactory) CreateProvider(providerName string) (Provider, error) {
	if providerName == "" {
		providerName = f.config.AI.Provider
	}

	switch strings.ToLower(providerName) {
	case "deepseek":
		return f.createDeepseekProvider()
	case "ollama":
		return f.createOllamaProvider()
	default:
		return nil, fmt.Errorf("unsupported provider: %s", providerName)
	}
}

// createDeepseekProvider creates a Deepseek provider
func (f *ProviderFactory) createDeepseekProvider() (Provider, error) {
	cfg := f.config.AI.Deepseek
	
	if cfg.APIKey == "" {
		return nil, fmt.Errorf("deepseek API key is required")
	}
	
	if cfg.Model == "" {
		cfg.Model = "deepseek-chat"
	}
	
	if cfg.BaseURL == "" {
		cfg.BaseURL = "https://api.deepseek.com/v1"
	}
	
	return NewDeepseekProvider(cfg.APIKey, cfg.BaseURL, cfg.Model), nil
}

// createOllamaProvider creates an Ollama provider
func (f *ProviderFactory) createOllamaProvider() (Provider, error) {
	cfg := f.config.AI.Ollama
	
	if cfg.Host == "" {
		cfg.Host = "http://localhost:11434"
	}
	
	if cfg.Model == "" {
		cfg.Model = "llama3.2"
	}
	
	provider := NewOllamaProvider(cfg.Host, cfg.Model)
	
	// Test connection
	ctx := context.Background()
	if err := provider.IsAvailable(ctx); err != nil {
		return nil, fmt.Errorf("ollama provider not available: %w", err)
	}
	
	return provider, nil
}

// GetAvailableProviders returns a list of available providers
func (f *ProviderFactory) GetAvailableProviders() []string {
	return []string{"deepseek", "ollama"}
}

// ValidateProvider validates a provider configuration
func (f *ProviderFactory) ValidateProvider(providerName string) error {
	switch strings.ToLower(providerName) {
	case "deepseek":
		return f.validateDeepseekConfig()
	case "ollama":
		return f.validateOllamaConfig()
	default:
		return fmt.Errorf("unsupported provider: %s", providerName)
	}
}

// validateDeepseekConfig validates Deepseek configuration
func (f *ProviderFactory) validateDeepseekConfig() error {
	cfg := f.config.AI.Deepseek
	
	if cfg.APIKey == "" {
		return fmt.Errorf("deepseek API key is required")
	}
	
	if cfg.BaseURL == "" {
		return fmt.Errorf("deepseek base URL is required")
	}
	
	if cfg.Model == "" {
		return fmt.Errorf("deepseek model is required")
	}
	
	// Validate model name
	validModels := []string{"deepseek-chat", "deepseek-reasoner"}
	for _, validModel := range validModels {
		if cfg.Model == validModel {
			return nil
		}
	}
	
	return fmt.Errorf("invalid deepseek model: %s (valid models: %s)", 
		cfg.Model, strings.Join(validModels, ", "))
}

// validateOllamaConfig validates Ollama configuration
func (f *ProviderFactory) validateOllamaConfig() error {
	cfg := f.config.AI.Ollama
	
	if cfg.Host == "" {
		return fmt.Errorf("ollama host is required")
	}
	
	if cfg.Model == "" {
		return fmt.Errorf("ollama model is required")
	}
	
	return nil
}

// TestProvider tests if a provider is working correctly
func (f *ProviderFactory) TestProvider(providerName string) error {
	provider, err := f.CreateProvider(providerName)
	if err != nil {
		return fmt.Errorf("failed to create provider: %w", err)
	}
	
	ctx := context.Background()
	
	// Test availability
	if err := provider.IsAvailable(ctx); err != nil {
		return fmt.Errorf("provider not available: %w", err)
	}
	
	// Test basic chat functionality
	request := &ChatRequest{
		Messages: []Message{
			{
				Role:    "user",
				Content: "Hello, this is a test message. Please respond with 'Test successful'.",
			},
		},
		Temperature: 0.1,
		MaxTokens:   50,
	}
	
	response, err := provider.Chat(ctx, request)
	if err != nil {
		return fmt.Errorf("test chat failed: %w", err)
	}
	
	if len(response.Choices) == 0 {
		return fmt.Errorf("no response received from provider")
	}
	
	return nil
}

// GetProviderInfo returns information about a provider
func (f *ProviderFactory) GetProviderInfo(providerName string) (*ProviderInfo, error) {
	switch strings.ToLower(providerName) {
	case "deepseek":
		return &ProviderInfo{
			Name:        "Deepseek",
			Type:        "cloud",
			Description: "Deepseek AI cloud service with advanced reasoning capabilities",
			Models:      []string{"deepseek-chat", "deepseek-reasoner"},
			Features:    []string{"chat", "reasoning", "function_calling", "streaming"},
			RequiresKey: true,
		}, nil
	case "ollama":
		return &ProviderInfo{
			Name:        "Ollama",
			Type:        "local",
			Description: "Local AI models running through Ollama",
			Models:      []string{"llama3.2", "codellama", "mistral", "phi3"},
			Features:    []string{"chat", "function_calling", "streaming", "offline"},
			RequiresKey: false,
		}, nil
	default:
		return nil, fmt.Errorf("unknown provider: %s", providerName)
	}
}

// ProviderInfo contains information about a provider
type ProviderInfo struct {
	Name        string   `json:"name"`
	Type        string   `json:"type"` // "cloud" or "local"
	Description string   `json:"description"`
	Models      []string `json:"models"`
	Features    []string `json:"features"`
	RequiresKey bool     `json:"requires_key"`
}

// GetRecommendedProvider returns the recommended provider based on configuration
func (f *ProviderFactory) GetRecommendedProvider() string {
	// If Deepseek is configured with API key, prefer it
	if f.config.AI.Deepseek.APIKey != "" {
		return "deepseek"
	}
	
	// Otherwise, try Ollama if it's available
	if f.validateOllamaConfig() == nil {
		return "ollama"
	}
	
	// Default to deepseek
	return "deepseek"
}

// SwitchProvider switches the active provider in configuration
func (f *ProviderFactory) SwitchProvider(providerName string) error {
	// Validate the provider first
	if err := f.ValidateProvider(providerName); err != nil {
		return fmt.Errorf("cannot switch to provider %s: %w", providerName, err)
	}
	
	// Test the provider
	if err := f.TestProvider(providerName); err != nil {
		return fmt.Errorf("provider %s failed test: %w", providerName, err)
	}
	
	// Update configuration
	f.config.AI.Provider = providerName
	
	return nil
}

// GetCurrentProvider returns the currently configured provider
func (f *ProviderFactory) GetCurrentProvider() string {
	return f.config.AI.Provider
}

// ListAvailableModels returns available models for a provider
func (f *ProviderFactory) ListAvailableModels(providerName string) ([]string, error) {
	info, err := f.GetProviderInfo(providerName)
	if err != nil {
		return nil, err
	}
	
	return info.Models, nil
}

// GetProviderStatus returns the status of all providers
func (f *ProviderFactory) GetProviderStatus() map[string]ProviderStatus {
	status := make(map[string]ProviderStatus)
	
	for _, providerName := range f.GetAvailableProviders() {
		providerStatus := ProviderStatus{
			Name:      providerName,
			Available: false,
			Error:     "",
		}
		
		// Test provider
		if err := f.TestProvider(providerName); err != nil {
			providerStatus.Error = err.Error()
		} else {
			providerStatus.Available = true
		}
		
		status[providerName] = providerStatus
	}
	
	return status
}

// ProviderStatus represents the status of a provider
type ProviderStatus struct {
	Name      string `json:"name"`
	Available bool   `json:"available"`
	Error     string `json:"error,omitempty"`
}
